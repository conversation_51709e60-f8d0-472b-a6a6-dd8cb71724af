import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Layout from './components/Layout';
import HomePage from './pages/HomePage';
import ProjectsPage from './pages/ProjectsPage';
import ProjectDetailPage from './pages/ProjectDetailPage';
import ForumPage from './pages/ForumPage';
import DiscussionPage from './pages/DiscussionPage';

function App() {
  return (
    <Router>
      <Layout>
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/projects" element={<ProjectsPage />} />
          <Route path="/projects/:id" element={<ProjectDetailPage />} />
          <Route path="/forum" element={<ForumPage />} />
          <Route path="/discussion/:id" element={<DiscussionPage />} />
        </Routes>
      </Layout>
    </Router>
  );
}

export default App;
