import React, { useState, useMemo } from 'react';
import { Plus, Search, MessageSquare, HelpCircle, TrendingUp, Tag } from 'lucide-react';
import { mockDiscussions } from '../data/mockData';
import DiscussionCard from '../components/DiscussionCard';

const ForumPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('all');
  const [selectedTag, setSelectedTag] = useState('all');
  const [sortBy, setSortBy] = useState('latest');

  const allTags = Array.from(new Set(mockDiscussions.flatMap(d => d.tags)));

  const filteredDiscussions = useMemo(() => {
    let filtered = mockDiscussions.filter(discussion => {
      const matchesSearch = discussion.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           discussion.content.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesType = selectedType === 'all' ||
                         (selectedType === 'questions' && discussion.isQuestion) ||
                         (selectedType === 'discussions' && !discussion.isQuestion);
      const matchesTag = selectedTag === 'all' || discussion.tags.includes(selectedTag);

      return matchesSearch && matchesType && matchesTag;
    });

    // Sort discussions
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'latest':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'oldest':
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        case 'mostVoted':
          return (b.upvotes - b.downvotes) - (a.upvotes - a.downvotes);
        case 'mostViewed':
          return b.views - a.views;
        case 'mostReplied':
          return b.replies.length - a.replies.length;
        default:
          return 0;
      }
    });

    return filtered;
  }, [searchTerm, selectedType, selectedTag, sortBy]);

  const stats = {
    total: mockDiscussions.length,
    questions: mockDiscussions.filter(d => d.isQuestion).length,
    discussions: mockDiscussions.filter(d => !d.isQuestion).length,
    answered: mockDiscussions.filter(d => d.isAnswered).length,
    unanswered: mockDiscussions.filter(d => d.isQuestion && !d.isAnswered).length
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Project Crypto Land Community Forum</h1>
          <p className="text-gray-600">
            Ask questions, share insights, and discuss everything crypto with our community.
          </p>
        </div>
        <button className="btn-primary mt-4 sm:mt-0 flex items-center space-x-2">
          <Plus className="w-4 h-4" />
          <span>New Discussion</span>
        </button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <div className="card text-center">
          <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
          <div className="text-sm text-gray-500">Total</div>
        </div>
        <div className="card text-center">
          <div className="text-2xl font-bold text-blue-600">{stats.questions}</div>
          <div className="text-sm text-gray-500">Questions</div>
        </div>
        <div className="card text-center">
          <div className="text-2xl font-bold text-green-600">{stats.discussions}</div>
          <div className="text-sm text-gray-500">Discussions</div>
        </div>
        <div className="card text-center">
          <div className="text-2xl font-bold text-green-600">{stats.answered}</div>
          <div className="text-sm text-gray-500">Answered</div>
        </div>
        <div className="card text-center">
          <div className="text-2xl font-bold text-orange-600">{stats.unanswered}</div>
          <div className="text-sm text-gray-500">Unanswered</div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="card">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          {/* Search */}
          <div className="md:col-span-2">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search discussions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input-field pl-10"
              />
            </div>
          </div>

          {/* Type Filter */}
          <div>
            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              className="input-field"
            >
              <option value="all">All Types</option>
              <option value="questions">Questions</option>
              <option value="discussions">Discussions</option>
            </select>
          </div>

          {/* Tag Filter */}
          <div>
            <select
              value={selectedTag}
              onChange={(e) => setSelectedTag(e.target.value)}
              className="input-field"
            >
              <option value="all">All Tags</option>
              {allTags.map(tag => (
                <option key={tag} value={tag}>{tag}</option>
              ))}
            </select>
          </div>

          {/* Sort */}
          <div>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="input-field"
            >
              <option value="latest">Latest</option>
              <option value="oldest">Oldest</option>
              <option value="mostVoted">Most Voted</option>
              <option value="mostViewed">Most Viewed</option>
              <option value="mostReplied">Most Replied</option>
            </select>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <button className="card hover:shadow-md transition-shadow text-left p-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <HelpCircle className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <div className="font-medium text-gray-900">Ask a Question</div>
              <div className="text-sm text-gray-500">Get help from the community</div>
            </div>
          </div>
        </button>

        <button className="card hover:shadow-md transition-shadow text-left p-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
              <MessageSquare className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <div className="font-medium text-gray-900">Start Discussion</div>
              <div className="text-sm text-gray-500">Share your insights</div>
            </div>
          </div>
        </button>

        <button className="card hover:shadow-md transition-shadow text-left p-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
              <TrendingUp className="w-5 h-5 text-purple-600" />
            </div>
            <div>
              <div className="font-medium text-gray-900">Trending</div>
              <div className="text-sm text-gray-500">See what's hot</div>
            </div>
          </div>
        </button>
      </div>

      {/* Results Count */}
      <div className="flex items-center justify-between">
        <p className="text-gray-600">
          Showing {filteredDiscussions.length} of {mockDiscussions.length} discussions
        </p>
        {(searchTerm || selectedType !== 'all' || selectedTag !== 'all') && (
          <button
            onClick={() => {
              setSearchTerm('');
              setSelectedType('all');
              setSelectedTag('all');
            }}
            className="text-primary-600 hover:text-primary-700 text-sm"
          >
            Clear all filters
          </button>
        )}
      </div>

      {/* Discussions List */}
      {filteredDiscussions.length > 0 ? (
        <div className="space-y-4">
          {filteredDiscussions.map((discussion) => (
            <DiscussionCard key={discussion.id} discussion={discussion} />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <MessageSquare className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No discussions found</h3>
          <p className="text-gray-500 mb-4">
            Try adjusting your search terms or filters to find what you're looking for.
          </p>
          <button
            onClick={() => {
              setSearchTerm('');
              setSelectedType('all');
              setSelectedTag('all');
            }}
            className="btn-primary"
          >
            Clear all filters
          </button>
        </div>
      )}

      {/* Popular Tags */}
      <div className="card">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Popular Tags</h3>
        <div className="flex flex-wrap gap-2">
          {allTags.map(tag => (
            <button
              key={tag}
              onClick={() => setSelectedTag(tag)}
              className={`badge text-sm px-3 py-1 cursor-pointer transition-colors ${
                selectedTag === tag
                  ? 'badge-info bg-primary-100 text-primary-800'
                  : 'badge-info hover:bg-gray-200'
              }`}
            >
              <Tag className="w-3 h-3 mr-1" />
              {tag}
            </button>
          ))}
        </div>
      </div>

      {/* Community Guidelines */}
      <div className="card bg-blue-50 border-blue-200">
        <h3 className="text-lg font-semibold text-blue-900 mb-3">Community Guidelines</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
          <div>
            <div className="font-medium mb-2">✅ Do's:</div>
            <ul className="space-y-1">
              <li>• Be respectful and constructive</li>
              <li>• Provide helpful and accurate information</li>
              <li>• Use appropriate tags for your posts</li>
              <li>• Upvote helpful content</li>
            </ul>
          </div>
          <div>
            <div className="font-medium mb-2">❌ Don'ts:</div>
            <ul className="space-y-1">
              <li>• Spam or promote scams</li>
              <li>• Attack or harass other users</li>
              <li>• Post misleading information</li>
              <li>• Use multiple accounts to manipulate votes</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ForumPage;
