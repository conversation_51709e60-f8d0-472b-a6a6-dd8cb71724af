import React, { useState, useEffect } from 'react';
import {
  Home,
  TrendingUp,
  MessageSquare,
  PenTool,
  Wallet,
  Menu,
  X,
  ChevronUp,
  Sparkles,
  Target,
  Users
} from 'lucide-react';
import { mockProjects, mockMarketData } from '../data/mockData';
import WalletConnect from '../components/WalletConnect';
import TopProjectsShowcase from '../components/TopProjectsShowcase';
import ForumSection from '../components/ForumSection';
import UserPostsSection from '../components/UserPostsSection';

const SinglePageApp: React.FC = () => {
  const [userAddress, setUserAddress] = useState<string | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [activeSection, setActiveSection] = useState('home');
  const [showMobileMenu, setShowMobileMenu] = useState(false);
  const [showScrollTop, setShowScrollTop] = useState(false);

  // Handle scroll events
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 500);

      // Update active section based on scroll position
      const sections = ['home', 'projects', 'forum', 'posts', 'wallet'];
      const currentSection = sections.find(section => {
        const element = document.getElementById(section);
        if (element) {
          const rect = element.getBoundingClientRect();
          return rect.top <= 100 && rect.bottom >= 100;
        }
        return false;
      });
      
      if (currentSection) {
        setActiveSection(currentSection);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleWalletConnect = (address: string) => {
    setUserAddress(address);
    setIsConnected(true);
  };

  const handleWalletDisconnect = () => {
    setUserAddress(null);
    setIsConnected(false);
  };

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
    setShowMobileMenu(false);
  };

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const formatNumber = (num: number): string => {
    if (num >= 1e12) return (num / 1e12).toFixed(2) + 'T';
    if (num >= 1e9) return (num / 1e9).toFixed(2) + 'B';
    if (num >= 1e6) return (num / 1e6).toFixed(2) + 'M';
    if (num >= 1e3) return (num / 1e3).toFixed(2) + 'K';
    return num.toString();
  };

  const navigationItems = [
    { id: 'home', label: 'Home', icon: Home },
    { id: 'projects', label: 'Projects', icon: TrendingUp },
    { id: 'forum', label: 'Forum', icon: MessageSquare },
    { id: 'posts', label: 'Posts', icon: PenTool },
    { id: 'wallet', label: 'Wallet', icon: Wallet }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Sticky Navigation */}
      <nav className="sticky top-0 z-50 bg-white/95 backdrop-blur-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-r from-primary-600 to-purple-600 rounded-lg flex items-center justify-center">
                <Sparkles className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold text-gray-900">CryptoLand</span>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-8">
              {navigationItems.map((item) => {
                const Icon = item.icon;
                return (
                  <button
                    key={item.id}
                    onClick={() => scrollToSection(item.id)}
                    className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                      activeSection === item.id
                        ? 'bg-primary-100 text-primary-700'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{item.label}</span>
                  </button>
                );
              })}
            </div>

            {/* Wallet Status */}
            <div className="hidden md:flex items-center space-x-3">
              {isConnected ? (
                <div className="flex items-center space-x-2 px-3 py-2 bg-green-100 text-green-700 rounded-lg">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm font-medium">
                    {userAddress?.slice(0, 6)}...{userAddress?.slice(-4)}
                  </span>
                </div>
              ) : (
                <button
                  onClick={() => scrollToSection('wallet')}
                  className="btn-primary text-sm px-4 py-2"
                >
                  Connect Wallet
                </button>
              )}
            </div>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setShowMobileMenu(!showMobileMenu)}
              className="md:hidden p-2 text-gray-600 hover:text-gray-900"
            >
              {showMobileMenu ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {showMobileMenu && (
          <div className="md:hidden bg-white border-t border-gray-200">
            <div className="px-4 py-2 space-y-1">
              {navigationItems.map((item) => {
                const Icon = item.icon;
                return (
                  <button
                    key={item.id}
                    onClick={() => scrollToSection(item.id)}
                    className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                      activeSection === item.id
                        ? 'bg-primary-100 text-primary-700'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{item.label}</span>
                  </button>
                );
              })}
            </div>
          </div>
        )}
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Hero Section */}
        <section id="home" className="mb-16">
          <div className="relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-primary-600 via-purple-600 to-pink-600 rounded-2xl"></div>
            <div className="absolute inset-0 bg-black/20 rounded-2xl"></div>
            <div className="relative text-center py-20 px-8">
              <div className="flex items-center justify-center mb-6">
                <div className="flex items-center space-x-2 px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full">
                  <Sparkles className="w-5 h-5 text-yellow-300" />
                  <span className="text-white font-medium">All-in-One Crypto Platform</span>
                </div>
              </div>
              
              <h1 className="text-5xl font-bold mb-6 text-white">
                Discover, Analyze & Discuss
                <span className="block bg-gradient-to-r from-yellow-300 to-pink-300 bg-clip-text text-transparent">
                  Cryptocurrency Projects
                </span>
              </h1>
              
              <p className="text-xl text-white/90 mb-10 max-w-3xl mx-auto leading-relaxed">
                Everything you need in one place: project analysis, community discussions, 
                market insights, and wallet integration. Join thousands of crypto enthusiasts.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-6 justify-center mb-8">
                <button 
                  onClick={() => scrollToSection('projects')}
                  className="btn-primary bg-white text-primary-600 hover:bg-gray-100 px-8 py-4 text-lg font-semibold flex items-center justify-center space-x-2 group"
                >
                  <Target className="w-5 h-5" />
                  <span>Explore Projects</span>
                </button>
                <button 
                  onClick={() => scrollToSection('forum')}
                  className="btn-secondary bg-transparent border-2 border-white text-white hover:bg-white hover:text-primary-600 px-8 py-4 text-lg font-semibold flex items-center justify-center space-x-2 group"
                >
                  <Users className="w-5 h-5" />
                  <span>Join Community</span>
                </button>
              </div>

              {/* Quick Stats */}
              <div className="grid grid-cols-1 sm:grid-cols-4 gap-6 max-w-4xl mx-auto">
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-1">
                    ${formatNumber(mockMarketData.totalMarketCap)}
                  </div>
                  <div className="text-white/80 text-sm">Total Market Cap</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-1">2,847+</div>
                  <div className="text-white/80 text-sm">Active Projects</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-1">15K+</div>
                  <div className="text-white/80 text-sm">Community Members</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-1">50K+</div>
                  <div className="text-white/80 text-sm">Discussions</div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Projects Section */}
        <section id="projects" className="mb-16">
          <TopProjectsShowcase 
            projects={mockProjects} 
            title="Top Crypto Projects"
            showFilters={true}
            variant="grid"
          />
        </section>

        {/* Forum Section */}
        <section id="forum" className="mb-16">
          <ForumSection 
            userAddress={userAddress || undefined}
            isConnected={isConnected}
          />
        </section>

        {/* Posts Section */}
        <section id="posts" className="mb-16">
          <UserPostsSection 
            userAddress={userAddress || undefined}
            isConnected={isConnected}
          />
        </section>

        {/* Wallet Section */}
        <section id="wallet" className="mb-16">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Connect Your Wallet</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Connect your crypto wallet to unlock all features: participate in discussions, 
              create posts, vote on content, and build your reputation in the community.
            </p>
          </div>
          <div className="max-w-md mx-auto">
            <WalletConnect 
              onConnect={handleWalletConnect}
              onDisconnect={handleWalletDisconnect}
            />
          </div>
        </section>
      </main>

      {/* Scroll to Top Button */}
      {showScrollTop && (
        <button
          onClick={scrollToTop}
          className="fixed bottom-8 right-8 w-12 h-12 bg-primary-600 text-white rounded-full shadow-lg hover:bg-primary-700 transition-colors flex items-center justify-center z-40"
        >
          <ChevronUp className="w-5 h-5" />
        </button>
      )}
    </div>
  );
};

export default SinglePageApp;
