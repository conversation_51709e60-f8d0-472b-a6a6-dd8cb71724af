import React from 'react';
import { Link } from 'react-router-dom';
import { TrendingUp, DollarSign, BarChart3, Users } from 'lucide-react';
import { mockMarketData, mockTrendingProjects, mockDiscussions, mockProjects } from '../data/mockData';
import ProjectCard from '../components/ProjectCard';
import DiscussionCard from '../components/DiscussionCard';

const HomePage: React.FC = () => {
  const formatNumber = (num: number): string => {
    if (num >= 1e12) return (num / 1e12).toFixed(2) + 'T';
    if (num >= 1e9) return (num / 1e9).toFixed(2) + 'B';
    if (num >= 1e6) return (num / 1e6).toFixed(2) + 'M';
    return num.toString();
  };

  return (
    <div className="space-y-8">
      {/* Hero Section */}
              <div className="text-center py-12 bg-gradient-to-r from-primary-600 to-purple-600 rounded-2xl text-white">
          <h1 className="text-4xl font-bold mb-4">
            Welcome to Project Crypto Land
          </h1>
          <p className="text-xl mb-8 text-primary-100 max-w-2xl mx-auto">
            Discover, analyze, and discuss the latest cryptocurrency projects. Join our community of crypto enthusiasts, developers, and investors.
          </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link to="/projects" className="btn-secondary bg-white text-primary-600 hover:bg-gray-100">
            Explore Projects
          </Link>
          <Link to="/forum" className="btn-secondary bg-transparent border-white text-white hover:bg-white hover:text-primary-600">
            Join Discussion
          </Link>
        </div>
      </div>

      {/* Market Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="card text-center">
          <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mx-auto mb-3">
            <DollarSign className="w-6 h-6 text-green-600" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-1">
            ${formatNumber(mockMarketData.totalMarketCap)}
          </h3>
          <p className="text-sm text-gray-500">Total Market Cap</p>
        </div>

        <div className="card text-center">
          <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mx-auto mb-3">
            <BarChart3 className="w-6 h-6 text-blue-600" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-1">
            ${formatNumber(mockMarketData.totalVolume24h)}
          </h3>
          <p className="text-sm text-gray-500">24h Volume</p>
        </div>

        <div className="card text-center">
          <div className="flex items-center justify-center w-12 h-12 bg-yellow-100 rounded-lg mx-auto mb-3">
            <TrendingUp className="w-6 h-6 text-yellow-600" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-1">
            {mockMarketData.btcDominance.toFixed(1)}%
          </h3>
          <p className="text-sm text-gray-500">BTC Dominance</p>
        </div>

        <div className="card text-center">
          <div className="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg mx-auto mb-3">
            <Users className="w-6 h-6 text-purple-600" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-1">
            {mockMarketData.marketChange24h >= 0 ? '+' : ''}{mockMarketData.marketChange24h.toFixed(1)}%
          </h3>
          <p className="text-sm text-gray-500">Market Change 24h</p>
        </div>
      </div>

      {/* Top Projects */}
      <div>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900">Top Projects</h2>
          <Link to="/projects" className="text-primary-600 hover:text-primary-700 font-medium">
            View All →
          </Link>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {mockProjects.slice(0, 6).map((project) => (
            <ProjectCard key={project.id} project={project} />
          ))}
        </div>
      </div>

      {/* Trending Projects */}
      <div>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900">Trending</h2>
          <Link to="/projects" className="text-primary-600 hover:text-primary-700 font-medium">
            View All →
          </Link>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {mockTrendingProjects.map((project) => (
            <div key={project.id} className="card">
              <div className="flex items-center space-x-3 mb-4">
                <img
                  src={project.logo}
                  alt={project.name}
                  className="w-10 h-10 rounded-lg"
                  onError={(e) => {
                    e.currentTarget.src = 'https://via.placeholder.com/40x40/6366f1/ffffff?text=' + project.symbol.charAt(0);
                  }}
                />
                <div>
                  <h3 className="font-semibold text-gray-900">{project.name}</h3>
                  <p className="text-sm text-gray-500">{project.symbol}</p>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">24h Change</span>
                  <span className={`font-medium ${
                    project.priceChange24h >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {project.priceChange24h >= 0 ? '+' : ''}{project.priceChange24h.toFixed(1)}%
                  </span>
                </div>

                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Volume</span>
                  <span className="font-medium">${formatNumber(project.volume24h)}</span>
                </div>

                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Social Score</span>
                  <span className="font-medium">{project.socialScore}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Discussions */}
      <div>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900">Recent Discussions</h2>
          <Link to="/forum" className="text-primary-600 hover:text-primary-700 font-medium">
            View All →
          </Link>
        </div>
        <div className="space-y-4">
          {mockDiscussions.map((discussion) => (
            <DiscussionCard key={discussion.id} discussion={discussion} />
          ))}
        </div>
      </div>

      {/* Call to Action */}
      <div className="text-center py-12 bg-gray-100 rounded-2xl">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          Ready to dive deeper?
        </h2>
        <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
          Join thousands of crypto enthusiasts in discussions, analysis, and discovery of the next big thing in cryptocurrency.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link to="/forum" className="btn-primary">
            Start a Discussion
          </Link>
          <Link to="/projects" className="btn-secondary">
            Browse Projects
          </Link>
        </div>
      </div>
    </div>
  );
};

export default HomePage;
