import React from 'react';
import { Link } from 'react-router-dom';
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  BarChart3,
  Users,
  Sparkles,
  Target,
  Zap,
  ArrowUpRight,
  Activity,
  Globe,
  Star,
  Award
} from 'lucide-react';
import { mockMarketData, mockTrendingProjects, mockDiscussions, mockProjects } from '../data/mockData';
import ProjectCard from '../components/ProjectCard';
import DiscussionCard from '../components/DiscussionCard';
import AnalyticsDashboard from '../components/AnalyticsDashboard';
import TopProjectsShowcase from '../components/TopProjectsShowcase';

const HomePage: React.FC = () => {
  const formatNumber = (num: number): string => {
    if (num >= 1e12) return (num / 1e12).toFixed(2) + 'T';
    if (num >= 1e9) return (num / 1e9).toFixed(2) + 'B';
    if (num >= 1e6) return (num / 1e6).toFixed(2) + 'M';
    return num.toString();
  };

  return (
    <div className="space-y-12">
      {/* Enhanced Hero Section */}
      <div className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary-600 via-purple-600 to-pink-600 rounded-2xl"></div>
        <div className="absolute inset-0 bg-black/20 rounded-2xl"></div>
        <div className="relative text-center py-16 px-8">
          <div className="flex items-center justify-center mb-4">
            <div className="flex items-center space-x-2 px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full">
              <Sparkles className="w-5 h-5 text-yellow-300" />
              <span className="text-white font-medium">Live Market Data</span>
            </div>
          </div>

          <h1 className="text-5xl font-bold mb-6 text-white">
            Discover the Future of
            <span className="block bg-gradient-to-r from-yellow-300 to-pink-300 bg-clip-text text-transparent">
              Cryptocurrency
            </span>
          </h1>

          <p className="text-xl mb-8 text-white/90 max-w-3xl mx-auto leading-relaxed">
            Analyze market trends, discover promising projects, and connect with a community of crypto enthusiasts.
            Make informed decisions with real-time data and expert insights.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
            <Link
              to="/projects"
              className="btn-primary bg-white text-primary-600 hover:bg-gray-100 px-8 py-3 text-lg font-semibold flex items-center justify-center space-x-2"
            >
              <Target className="w-5 h-5" />
              <span>Explore Projects</span>
            </Link>
            <Link
              to="/forum"
              className="btn-secondary bg-transparent border-2 border-white text-white hover:bg-white hover:text-primary-600 px-8 py-3 text-lg font-semibold flex items-center justify-center space-x-2"
            >
              <Users className="w-5 h-5" />
              <span>Join Community</span>
            </Link>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 max-w-2xl mx-auto">
            <div className="text-center">
              <div className="text-3xl font-bold text-white mb-1">2,847+</div>
              <div className="text-white/80 text-sm">Active Projects</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-white mb-1">15K+</div>
              <div className="text-white/80 text-sm">Community Members</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-white mb-1">$2.1T</div>
              <div className="text-white/80 text-sm">Total Market Cap</div>
            </div>
          </div>
        </div>
      </div>

      {/* Market Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="card text-center">
          <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mx-auto mb-3">
            <DollarSign className="w-6 h-6 text-green-600" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-1">
            ${formatNumber(mockMarketData.totalMarketCap)}
          </h3>
          <p className="text-sm text-gray-500">Total Market Cap</p>
        </div>

        <div className="card text-center">
          <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mx-auto mb-3">
            <BarChart3 className="w-6 h-6 text-blue-600" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-1">
            ${formatNumber(mockMarketData.totalVolume24h)}
          </h3>
          <p className="text-sm text-gray-500">24h Volume</p>
        </div>

        <div className="card text-center">
          <div className="flex items-center justify-center w-12 h-12 bg-yellow-100 rounded-lg mx-auto mb-3">
            <TrendingUp className="w-6 h-6 text-yellow-600" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-1">
            {mockMarketData.btcDominance.toFixed(1)}%
          </h3>
          <p className="text-sm text-gray-500">BTC Dominance</p>
        </div>

        <div className="card text-center">
          <div className="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg mx-auto mb-3">
            <Users className="w-6 h-6 text-purple-600" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-1">
            {mockMarketData.marketChange24h >= 0 ? '+' : ''}{mockMarketData.marketChange24h.toFixed(1)}%
          </h3>
          <p className="text-sm text-gray-500">Market Change 24h</p>
        </div>
      </div>

      {/* Analytics Dashboard */}
      <div>
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <h2 className="text-2xl font-bold text-gray-900">Market Analytics</h2>
            <div className="flex items-center space-x-1 px-3 py-1 bg-green-100 rounded-full">
              <Activity className="w-4 h-4 text-green-600" />
              <span className="text-sm font-medium text-green-700">Live</span>
            </div>
          </div>
          <Link
            to="/analytics"
            className="text-primary-600 hover:text-primary-700 font-medium flex items-center space-x-1"
          >
            <span>View Full Analytics</span>
            <ArrowUpRight className="w-4 h-4" />
          </Link>
        </div>
        <AnalyticsDashboard marketData={mockMarketData} />
      </div>

      {/* Top Projects Showcase */}
      <TopProjectsShowcase
        projects={mockProjects}
        title="Top Performing Projects"
        showFilters={true}
        variant="grid"
      />

      {/* Market Insights & Trends */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Trending Projects */}
        <div>
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <h2 className="text-2xl font-bold text-gray-900">Trending Now</h2>
              <div className="flex items-center space-x-1 px-3 py-1 bg-orange-100 rounded-full">
                <Zap className="w-4 h-4 text-orange-600" />
                <span className="text-sm font-medium text-orange-700">Hot</span>
              </div>
            </div>
            <Link
              to="/projects?filter=trending"
              className="text-primary-600 hover:text-primary-700 font-medium flex items-center space-x-1"
            >
              <span>View All</span>
              <ArrowUpRight className="w-4 h-4" />
            </Link>
          </div>

          <div className="space-y-4">
            {mockTrendingProjects.map((project, index) => (
              <div key={project.id} className="card hover:shadow-md transition-all duration-300 hover:-translate-y-1">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-r from-orange-400 to-pink-500 rounded-full text-white font-bold text-sm">
                      {index + 1}
                    </div>
                    <img
                      src={project.logo}
                      alt={project.name}
                      className="w-10 h-10 rounded-lg shadow-sm"
                      onError={(e) => {
                        e.currentTarget.src = 'https://via.placeholder.com/40x40/6366f1/ffffff?text=' + project.symbol.charAt(0);
                      }}
                    />
                    <div>
                      <h3 className="font-semibold text-gray-900">{project.name}</h3>
                      <p className="text-sm text-gray-500">{project.symbol}</p>
                    </div>
                  </div>

                  <div className="text-right">
                    <div className="flex items-center space-x-2 mb-1">
                      {project.priceChange24h >= 0 ? (
                        <TrendingUp className="w-4 h-4 text-green-500" />
                      ) : (
                        <TrendingDown className="w-4 h-4 text-red-500" />
                      )}
                      <span className={`font-bold ${
                        project.priceChange24h >= 0 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {project.priceChange24h >= 0 ? '+' : ''}{project.priceChange24h.toFixed(2)}%
                      </span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-500">
                      <Globe className="w-3 h-3" />
                      <span>{project.socialScore}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Market Insights */}
        <div>
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <h2 className="text-2xl font-bold text-gray-900">Market Insights</h2>
              <div className="flex items-center space-x-1 px-3 py-1 bg-blue-100 rounded-full">
                <Star className="w-4 h-4 text-blue-600" />
                <span className="text-sm font-medium text-blue-700">AI Powered</span>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            {/* Insight Cards */}
            <div className="card bg-gradient-to-r from-green-50 to-emerald-50 border-green-200">
              <div className="flex items-start space-x-3">
                <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                  <TrendingUp className="w-5 h-5 text-white" />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 mb-2">Bullish Market Sentiment</h3>
                  <p className="text-sm text-gray-600 mb-3">
                    Market indicators show strong bullish sentiment with 73% of top projects showing positive momentum.
                  </p>
                  <div className="flex items-center space-x-2">
                    <span className="text-xs px-2 py-1 bg-green-100 text-green-700 rounded-full">High Confidence</span>
                    <span className="text-xs text-gray-500">Updated 2 min ago</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="card bg-gradient-to-r from-blue-50 to-cyan-50 border-blue-200">
              <div className="flex items-start space-x-3">
                <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                  <BarChart3 className="w-5 h-5 text-white" />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 mb-2">DeFi Sector Growth</h3>
                  <p className="text-sm text-gray-600 mb-3">
                    DeFi projects showing 15% average growth this week, outperforming traditional crypto sectors.
                  </p>
                  <div className="flex items-center space-x-2">
                    <span className="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded-full">Trending</span>
                    <span className="text-xs text-gray-500">Updated 5 min ago</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="card bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200">
              <div className="flex items-start space-x-3">
                <div className="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center">
                  <Award className="w-5 h-5 text-white" />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 mb-2">New Project Alert</h3>
                  <p className="text-sm text-gray-600 mb-3">
                    3 promising new projects launched this week with strong community backing and innovative technology.
                  </p>
                  <div className="flex items-center space-x-2">
                    <span className="text-xs px-2 py-1 bg-purple-100 text-purple-700 rounded-full">New</span>
                    <span className="text-xs text-gray-500">Updated 1 hour ago</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Discussions */}
      <div>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900">Recent Discussions</h2>
          <Link to="/forum" className="text-primary-600 hover:text-primary-700 font-medium">
            View All →
          </Link>
        </div>
        <div className="space-y-4">
          {mockDiscussions.map((discussion) => (
            <DiscussionCard key={discussion.id} discussion={discussion} />
          ))}
        </div>
      </div>

      {/* Enhanced Call to Action */}
      <div className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-primary-600 via-purple-600 to-pink-600 rounded-2xl"></div>
        <div className="absolute inset-0 bg-black/10 rounded-2xl"></div>
        <div className="relative text-center py-16 px-8">
          <div className="flex items-center justify-center mb-6">
            <div className="flex items-center space-x-2 px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full">
              <Sparkles className="w-5 h-5 text-yellow-300" />
              <span className="text-white font-medium">Join the Community</span>
            </div>
          </div>

          <h2 className="text-4xl font-bold text-white mb-6">
            Ready to Start Your
            <span className="block bg-gradient-to-r from-yellow-300 to-pink-300 bg-clip-text text-transparent">
              Crypto Journey?
            </span>
          </h2>

          <p className="text-xl text-white/90 mb-10 max-w-3xl mx-auto leading-relaxed">
            Connect with thousands of crypto enthusiasts, discover the next big opportunities,
            and make informed investment decisions with our comprehensive platform.
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center mb-8">
            <Link
              to="/forum"
              className="btn-primary bg-white text-primary-600 hover:bg-gray-100 px-8 py-4 text-lg font-semibold flex items-center justify-center space-x-2 group"
            >
              <Users className="w-5 h-5" />
              <span>Start Discussion</span>
              <ArrowUpRight className="w-4 h-4 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform" />
            </Link>
            <Link
              to="/projects"
              className="btn-secondary bg-transparent border-2 border-white text-white hover:bg-white hover:text-primary-600 px-8 py-4 text-lg font-semibold flex items-center justify-center space-x-2 group"
            >
              <Target className="w-5 h-5" />
              <span>Explore Projects</span>
              <ArrowUpRight className="w-4 h-4 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform" />
            </Link>
          </div>

          {/* Social Proof */}
          <div className="flex items-center justify-center space-x-8 text-white/80">
            <div className="text-center">
              <div className="text-2xl font-bold text-white">15K+</div>
              <div className="text-sm">Active Users</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-white">2.8K+</div>
              <div className="text-sm">Projects Listed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-white">50K+</div>
              <div className="text-sm">Discussions</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HomePage;
