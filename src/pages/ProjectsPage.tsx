import React, { useState, useMemo } from 'react';
import { Search, TrendingUp, TrendingDown, Shield, Users, Code } from 'lucide-react';
import { mockProjects } from '../data/mockData';
import ProjectCard from '../components/ProjectCard';

const ProjectsPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState('rank');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  const categories = ['all', ...Array.from(new Set(mockProjects.map(p => p.category)))];
  const sortOptions = [
    { value: 'rank', label: 'Rank' },
    { value: 'marketCap', label: 'Market Cap' },
    { value: 'price', label: 'Price' },
    { value: 'priceChange24h', label: '24h Change' },
    { value: 'volume24h', label: 'Volume' },
    { value: 'communityScore', label: 'Community Score' },
    { value: 'developerScore', label: 'Developer Score' }
  ];

  const filteredAndSortedProjects = useMemo(() => {
    let filtered = mockProjects.filter(project => {
      const matchesSearch = project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           project.symbol.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           project.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = selectedCategory === 'all' || project.category === selectedCategory;
      return matchesSearch && matchesCategory;
    });

        filtered.sort((a, b) => {
      let aValue = a[sortBy as keyof typeof a];
      let bValue = b[sortBy as keyof typeof b];

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });

    return filtered;
  }, [searchTerm, selectedCategory, sortBy, sortOrder]);

  const toggleSortOrder = () => {
    setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Project Crypto Land - Crypto Projects</h1>
        <p className="text-gray-600">
          Discover and analyze {mockProjects.length} cryptocurrency projects with real-time data and community insights.
        </p>
      </div>

      {/* Filters and Search */}
      <div className="card">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Search */}
          <div className="md:col-span-2">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search projects..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input-field pl-10"
              />
            </div>
          </div>

          {/* Category Filter */}
          <div>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="input-field"
            >
              {categories.map(category => (
                <option key={category} value={category}>
                  {category === 'all' ? 'All Categories' : category}
                </option>
              ))}
            </select>
          </div>

          {/* Sort */}
          <div className="flex space-x-2">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="input-field flex-1"
            >
              {sortOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            <button
              onClick={toggleSortOrder}
              className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              {sortOrder === 'asc' ? <TrendingUp className="w-4 h-4" /> : <TrendingDown className="w-4 h-4" />}
            </button>
          </div>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="card text-center">
          <div className="text-2xl font-bold text-gray-900">{filteredAndSortedProjects.length}</div>
          <div className="text-sm text-gray-500">Total Projects</div>
        </div>
        <div className="card text-center">
          <div className="text-2xl font-bold text-gray-900">
            {filteredAndSortedProjects.filter(p => p.verified).length}
          </div>
          <div className="text-sm text-gray-500">Verified Projects</div>
        </div>
        <div className="card text-center">
          <div className="text-2xl font-bold text-gray-900">
            {filteredAndSortedProjects.filter(p => p.priceChange24h > 0).length}
          </div>
          <div className="text-sm text-gray-500">Gaining Today</div>
        </div>
        <div className="card text-center">
          <div className="text-2xl font-bold text-gray-900">
            {filteredAndSortedProjects.filter(p => p.priceChange24h < 0).length}
          </div>
          <div className="text-sm text-gray-500">Declining Today</div>
        </div>
      </div>

      {/* Results Count */}
      <div className="flex items-center justify-between">
        <p className="text-gray-600">
          Showing {filteredAndSortedProjects.length} of {mockProjects.length} projects
        </p>
        {searchTerm && (
          <button
            onClick={() => setSearchTerm('')}
            className="text-primary-600 hover:text-primary-700 text-sm"
          >
            Clear search
          </button>
        )}
      </div>

      {/* Projects Grid */}
      {filteredAndSortedProjects.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredAndSortedProjects.map((project) => (
            <ProjectCard key={project.id} project={project} />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Search className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No projects found</h3>
          <p className="text-gray-500 mb-4">
            Try adjusting your search terms or filters to find what you're looking for.
          </p>
          <button
            onClick={() => {
              setSearchTerm('');
              setSelectedCategory('all');
            }}
            className="btn-primary"
          >
            Clear all filters
          </button>
        </div>
      )}

      {/* Quick Actions */}
      <div className="card">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <Shield className="w-6 h-6 text-blue-500" />
            <div className="text-left">
              <div className="font-medium text-gray-900">Verify Project</div>
              <div className="text-sm text-gray-500">Submit a project for verification</div>
            </div>
          </button>

          <button className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <Users className="w-6 h-6 text-green-500" />
            <div className="text-left">
              <div className="font-medium text-gray-900">Community Score</div>
              <div className="text-sm text-gray-500">Rate and review projects</div>
            </div>
          </button>

          <button className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <Code className="w-6 h-6 text-purple-500" />
            <div className="text-left">
              <div className="font-medium text-gray-900">Developer Tools</div>
              <div className="text-sm text-gray-500">Access developer resources</div>
            </div>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ProjectsPage;
