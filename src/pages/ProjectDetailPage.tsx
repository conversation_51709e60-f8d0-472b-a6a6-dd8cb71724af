import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import {
  ExternalLink,
  Shield,
  TrendingUp,
  TrendingDown,
  Users,
  Code,
  BarChart3,
  Globe,
  FileText,
  MessageSquare,
  Share2,
  Bookmark
} from 'lucide-react';
import { mockProjects, mockDiscussions } from '../data/mockData';
import DiscussionCard from '../components/DiscussionCard';

const ProjectDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [activeTab, setActiveTab] = useState('overview');

  const project = mockProjects.find(p => p.id === id);
  const relatedDiscussions = mockDiscussions.filter(d => d.projectId === id);

  if (!project) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Project not found</h2>
        <p className="text-gray-600 mb-6">The project you're looking for doesn't exist.</p>
        <Link to="/projects" className="btn-primary">
          Back to Projects
        </Link>
      </div>
    );
  }

  const formatNumber = (num: number): string => {
    if (num >= 1e12) return (num / 1e12).toFixed(2) + 'T';
    if (num >= 1e9) return (num / 1e9).toFixed(2) + 'B';
    if (num >= 1e6) return (num / 1e6).toFixed(2) + 'M';
    if (num >= 1e3) return (num / 1e3).toFixed(2) + 'K';
    return num.toString();
  };

  const formatPrice = (price: number): string => {
    if (price >= 1) return price.toFixed(2);
    if (price >= 0.01) return price.toFixed(4);
    return price.toFixed(8);
  };

  const getScoreColor = (score: number): string => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const tabs = [
    { id: 'overview', label: 'Overview', icon: BarChart3 },
    { id: 'social', label: 'Social & Links', icon: Users },
    { id: 'discussions', label: 'Discussions', icon: MessageSquare },
    { id: 'analysis', label: 'Analysis', icon: Code }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
        <div className="flex items-start space-x-4">
          <img
            src={project.logo}
            alt={project.name}
            className="w-20 h-20 rounded-xl"
            onError={(e) => {
              e.currentTarget.src = 'https://via.placeholder.com/80x80/6366f1/ffffff?text=' + project.symbol.charAt(0);
            }}
          />
          <div>
            <div className="flex items-center space-x-3 mb-2">
              <h1 className="text-3xl font-bold text-gray-900">{project.name}</h1>
              {project.verified && (
                <div className="flex items-center space-x-1 px-2 py-1 bg-blue-100 rounded-full">
                  <Shield className="w-4 h-4 text-blue-600" />
                  <span className="text-sm font-medium text-blue-600">Verified</span>
                </div>
              )}
            </div>
            <p className="text-xl text-gray-600 mb-2">{project.symbol}</p>
            <p className="text-gray-600 max-w-2xl">{project.description}</p>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-3">
          <button className="btn-secondary flex items-center space-x-2">
            <Bookmark className="w-4 h-4" />
            <span>Watchlist</span>
          </button>
          <button className="btn-secondary flex items-center space-x-2">
            <Share2 className="w-4 h-4" />
            <span>Share</span>
          </button>
        </div>
      </div>

      {/* Price and Stats */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Price</h3>
          <div className="space-y-4">
            <div>
              <div className="text-3xl font-bold text-gray-900">
                ${formatPrice(project.price)}
              </div>
              <div className={`flex items-center space-x-2 text-lg ${
                project.priceChange24h >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {project.priceChange24h >= 0 ? (
                  <TrendingUp className="w-5 h-5" />
                ) : (
                  <TrendingDown className="w-5 h-5" />
                )}
                <span className="font-medium">
                  {project.priceChange24h >= 0 ? '+' : ''}{project.priceChange24h.toFixed(2)}%
                </span>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-gray-500">Market Cap</p>
                <p className="font-medium">${formatNumber(project.marketCap)}</p>
              </div>
              <div>
                <p className="text-gray-500">Volume 24h</p>
                <p className="font-medium">${formatNumber(project.volume24h)}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Supply</h3>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-gray-500">Circulating</p>
                <p className="font-medium">{formatNumber(project.circulatingSupply)}</p>
              </div>
              <div>
                <p className="text-gray-500">Total</p>
                <p className="font-medium">{formatNumber(project.totalSupply)}</p>
              </div>
            </div>

            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-primary-600 h-2 rounded-full"
                style={{ width: `${(project.circulatingSupply / project.totalSupply) * 100}%` }}
              ></div>
            </div>
            <p className="text-xs text-gray-500 text-center">
              {((project.circulatingSupply / project.totalSupply) * 100).toFixed(1)}% of total supply circulating
            </p>
          </div>
        </div>

        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Scores</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Users className="w-4 h-4 text-blue-500" />
                <span className="text-sm text-gray-600">Community</span>
              </div>
              <span className={`font-medium ${getScoreColor(project.communityScore)}`}>
                {project.communityScore}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Code className="w-4 h-4 text-purple-500" />
                <span className="text-sm text-gray-600">Developer</span>
              </div>
              <span className={`font-medium ${getScoreColor(project.developerScore)}`}>
                {project.developerScore}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <BarChart3 className="w-4 h-4 text-green-500" />
                <span className="text-sm text-gray-600">Liquidity</span>
              </div>
              <span className={`font-medium ${getScoreColor(project.liquidityScore)}`}>
                {project.liquidityScore}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <TrendingUp className="w-4 h-4 text-yellow-500" />
                <span className="text-sm text-gray-600">Interest</span>
              </div>
              <span className={`font-medium ${getScoreColor(project.publicInterestScore)}`}>
                {project.publicInterestScore}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="card">
        <div className="border-b border-gray-200 mb-6">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                    activeTab === tab.id
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">About {project.name}</h3>
              <p className="text-gray-600 leading-relaxed">{project.description}</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Project Details</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-500">Category:</span>
                    <span className="font-medium">{project.category}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">Launch Date:</span>
                    <span className="font-medium">{new Date(project.launchDate).toLocaleDateString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">Rank:</span>
                    <span className="font-medium">#{project.rank}</span>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-medium text-gray-900 mb-3">Tags</h4>
                <div className="flex flex-wrap gap-2">
                  {project.tags.map((tag) => (
                    <span key={tag} className="badge badge-info">
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'social' && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Official Links</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {project.website && (
                  <a
                    href={project.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <Globe className="w-5 h-5 text-blue-500" />
                    <div>
                      <div className="font-medium text-gray-900">Website</div>
                      <div className="text-sm text-gray-500">{project.website}</div>
                    </div>
                    <ExternalLink className="w-4 h-4 text-gray-400 ml-auto" />
                  </a>
                )}

                {project.whitepaper && (
                  <a
                    href={project.whitepaper}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <FileText className="w-5 h-5 text-green-500" />
                    <div>
                      <div className="font-medium text-gray-900">Whitepaper</div>
                      <div className="text-sm text-gray-500">Technical documentation</div>
                    </div>
                    <ExternalLink className="w-4 h-4 text-gray-400 ml-auto" />
                  </a>
                )}
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Social Media</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(project.socialLinks).map(([platform, url]) => (
                  <a
                    key={platform}
                    href={url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className="w-5 h-5 bg-gray-400 rounded"></div>
                    <div>
                      <div className="font-medium text-gray-900 capitalize">{platform}</div>
                      <div className="text-sm text-gray-500">Follow us</div>
                    </div>
                    <ExternalLink className="w-4 h-4 text-gray-400 ml-auto" />
                  </a>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'discussions' && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">
                Discussions about {project.name}
              </h3>
              <Link to="/forum" className="btn-primary">
                Start Discussion
              </Link>
            </div>

            {relatedDiscussions.length > 0 ? (
              <div className="space-y-4">
                {relatedDiscussions.map((discussion) => (
                  <DiscussionCard key={discussion.id} discussion={discussion} />
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <MessageSquare className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No discussions yet</h3>
                <p className="text-gray-500 mb-4">
                  Be the first to start a discussion about {project.name}
                </p>
                <Link to="/forum" className="btn-primary">
                  Start Discussion
                </Link>
              </div>
            )}
          </div>
        )}

        {activeTab === 'analysis' && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Market Analysis</h3>
              <p className="text-gray-600">
                Detailed analysis and insights for {project.name} will be available here.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="card">
                <h4 className="font-medium text-gray-900 mb-3">Price Performance</h4>
                <div className="text-center py-8 text-gray-500">
                  Chart placeholder
                </div>
              </div>

              <div className="card">
                <h4 className="font-medium text-gray-900 mb-3">Volume Analysis</h4>
                <div className="text-center py-8 text-gray-500">
                  Chart placeholder
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProjectDetailPage;
