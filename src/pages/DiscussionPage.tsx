import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import {
  <PERSON>Left,
  ThumbsUp,
  ThumbsDown,
  MessageSquare,
  Share2,
  Bookmark,
  Flag,
  CheckCircle,
  User,
  Shield,
  Clock,
  Tag
} from 'lucide-react';
import { mockDiscussions } from '../data/mockData';

const DiscussionPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [showReplyForm, setShowReplyForm] = useState(false);
  const [replyContent, setReplyContent] = useState('');

  const discussion = mockDiscussions.find(d => d.id === id);

  if (!discussion) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Discussion not found</h2>
        <p className="text-gray-600 mb-6">The discussion you're looking for doesn't exist.</p>
        <Link to="/forum" className="btn-primary">
          Back to Forum
        </Link>
      </div>
    );
  }

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatNumber = (num: number): string => {
    if (num >= 1000) return (num / 1000).toFixed(1) + 'k';
    return num.toString();
  };

  const handleReply = () => {
    if (replyContent.trim()) {
      // Here you would typically send the reply to the backend
      console.log('Reply submitted:', replyContent);
      setReplyContent('');
      setShowReplyForm(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Back Button */}
      <Link
        to="/forum"
        className="inline-flex items-center space-x-2 text-primary-600 hover:text-primary-700 font-medium"
      >
        <ArrowLeft className="w-4 h-4" />
        <span>Back to Forum</span>
      </Link>

      {/* Discussion Header */}
      <div className="card">
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              {discussion.isQuestion ? (
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <MessageSquare className="w-5 h-5 text-blue-600" />
                </div>
              ) : (
                <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                  <MessageSquare className="w-5 h-5 text-green-600" />
                </div>
              )}

              {discussion.isAnswered && (
                <CheckCircle className="w-6 h-6 text-green-500" />
              )}
            </div>

            <div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">{discussion.title}</h1>

              <div className="flex items-center space-x-4 text-sm text-gray-500 mb-3">
                <div className="flex items-center space-x-1">
                  <User className="w-4 h-4" />
                  <span className="flex items-center space-x-1">
                    {discussion.author.username}
                    {discussion.author.isVerified && (
                      <Shield className="w-3 h-3 text-blue-500" />
                    )}
                  </span>
                </div>

                <div className="flex items-center space-x-1">
                  <Clock className="w-4 h-4" />
                  <span>{formatDate(discussion.createdAt)}</span>
                </div>

                <div className="flex items-center space-x-1">
                  <MessageSquare className="w-4 h-4" />
                  <span>{discussion.replies.length} replies</span>
                </div>
              </div>

              {/* Tags */}
              <div className="flex flex-wrap gap-2">
                {discussion.tags.map((tag) => (
                  <span key={tag} className="badge badge-info text-xs flex items-center space-x-1">
                    <Tag className="w-3 h-3" />
                    <span>{tag}</span>
                  </span>
                ))}
              </div>
            </div>
          </div>

          <div className="flex space-x-2">
            <button className="btn-secondary flex items-center space-x-2">
              <Bookmark className="w-4 h-4" />
              <span>Save</span>
            </button>
            <button className="btn-secondary flex items-center space-x-2">
              <Share2 className="w-4 h-4" />
              <span>Share</span>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="prose max-w-none">
          <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">
            {discussion.content}
          </p>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between pt-6 border-t border-gray-200">
          <div className="flex items-center space-x-6">
            <button className="flex items-center space-x-2 text-gray-500 hover:text-green-600 transition-colors">
              <ThumbsUp className="w-5 h-5" />
              <span>{formatNumber(discussion.upvotes)}</span>
            </button>

            <button className="flex items-center space-x-2 text-gray-500 hover:text-red-600 transition-colors">
              <ThumbsDown className="w-5 h-5" />
              <span>{formatNumber(discussion.downvotes)}</span>
            </button>

            <button
              onClick={() => setShowReplyForm(!showReplyForm)}
              className="flex items-center space-x-2 text-gray-500 hover:text-primary-600 transition-colors"
            >
              <MessageSquare className="w-5 h-5" />
              <span>Reply</span>
            </button>
          </div>

          <button className="flex items-center space-x-2 text-gray-500 hover:text-red-600 transition-colors">
            <Flag className="w-4 h-4" />
            <span className="text-sm">Report</span>
          </button>
        </div>
      </div>

      {/* Reply Form */}
      {showReplyForm && (
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Add Reply</h3>
          <textarea
            value={replyContent}
            onChange={(e) => setReplyContent(e.target.value)}
            placeholder="Write your reply..."
            rows={4}
            className="input-field resize-none"
          />
          <div className="flex justify-end space-x-3 mt-4">
            <button
              onClick={() => setShowReplyForm(false)}
              className="btn-secondary"
            >
              Cancel
            </button>
            <button
              onClick={handleReply}
              className="btn-primary"
              disabled={!replyContent.trim()}
            >
              Post Reply
            </button>
          </div>
        </div>
      )}

      {/* Replies */}
      <div className="space-y-4">
        <h2 className="text-xl font-bold text-gray-900">
          {discussion.replies.length} {discussion.replies.length === 1 ? 'Reply' : 'Replies'}
        </h2>

        {discussion.replies.length > 0 ? (
          discussion.replies.map((reply) => (
            <div key={reply.id} className="card">
              <div className="flex items-start space-x-3">
                <img
                  src={reply.author.avatar}
                  alt={reply.author.username}
                  className="w-10 h-10 rounded-full"
                />

                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="font-medium text-gray-900">
                      {reply.author.username}
                    </span>
                    {reply.author.isVerified && (
                      <Shield className="w-4 h-4 text-blue-500" />
                    )}
                    {reply.isBestAnswer && (
                      <CheckCircle className="w-4 h-4 text-green-500" />
                    )}
                    <span className="text-sm text-gray-500">
                      {formatDate(reply.createdAt)}
                    </span>
                  </div>

                  <div className="prose max-w-none mb-3">
                    <p className="text-gray-700">{reply.content}</p>
                  </div>

                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <button className="flex items-center space-x-1 hover:text-green-600 transition-colors">
                      <ThumbsUp className="w-4 h-4" />
                      <span>{formatNumber(reply.upvotes)}</span>
                    </button>

                    <button className="flex items-center space-x-1 hover:text-red-600 transition-colors">
                      <ThumbsDown className="w-4 h-4" />
                      <span>{formatNumber(reply.downvotes)}</span>
                    </button>

                    <button className="flex items-center space-x-1 hover:text-primary-600 transition-colors">
                      <MessageSquare className="w-4 h-4" />
                      <span>Reply</span>
                    </button>

                    {reply.isVerified && (
                      <span className="flex items-center space-x-1 text-green-600">
                        <CheckCircle className="w-4 h-4" />
                        <span>Verified</span>
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="text-center py-8">
            <MessageSquare className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No replies yet</h3>
            <p className="text-gray-500 mb-4">
              Be the first to reply to this {discussion.isQuestion ? 'question' : 'discussion'}
            </p>
            <button
              onClick={() => setShowReplyForm(true)}
              className="btn-primary"
            >
              Add Reply
            </button>
          </div>
        )}
      </div>

      {/* Related Discussions */}
      {discussion.projectId && (
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Related Discussions</h3>
          <p className="text-gray-600">
            More discussions about this project will appear here.
          </p>
        </div>
      )}

      {/* Community Guidelines */}
      <div className="card bg-blue-50 border-blue-200">
        <h3 className="text-lg font-semibold text-blue-900 mb-3">Community Guidelines</h3>
        <p className="text-blue-800 text-sm mb-3">
          Please keep discussions respectful and constructive. Remember to:
        </p>
        <ul className="text-blue-800 text-sm space-y-1">
          <li>• Provide helpful and accurate information</li>
          <li>• Be respectful to other community members</li>
          <li>• Use appropriate language and tone</li>
          <li>• Report inappropriate content</li>
        </ul>
      </div>
    </div>
  );
};

export default DiscussionPage;
