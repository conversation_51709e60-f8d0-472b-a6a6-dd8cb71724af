import { CryptoProject, Discussion, MarketData, TrendingProject, User } from '../types';

export const mockUsers: User[] = [
  {
    id: '1',
    username: 'CryptoWhale',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=CryptoWhale',
    reputation: 1250,
    joinDate: '2023-01-15',
    isVerified: true,
    badges: [
      { id: '1', name: '<PERSON><PERSON>', description: 'Crypto Expert', icon: '⭐', color: 'yellow' },
      { id: '2', name: 'Contributor', description: 'Top Contributor', icon: '🏆', color: 'blue' }
    ]
  },
  {
    id: '2',
    username: '<PERSON><PERSON>i<PERSON>ev',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=DeFiDev',
    reputation: 890,
    joinDate: '2023-03-20',
    isVerified: true,
    badges: [
      { id: '3', name: 'Developer', description: 'Smart Contract Developer', icon: '💻', color: 'purple' }
    ]
  },
  {
    id: '3',
    username: 'NewbieTrader',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=NewbieTrader',
    reputation: 150,
    joinDate: '2024-01-10',
    isVerified: false,
    badges: []
  }
];

export const mockProjects: CryptoProject[] = [
  {
    id: '1',
    name: 'Ethereum',
    symbol: 'ETH',
    logo: 'https://cryptologos.cc/logos/ethereum-eth-logo.png',
    description: 'Ethereum is a decentralized, open-source blockchain with smart contract functionality.',
    category: 'Smart Contract Platform',
    marketCap: 450000000000,
    price: 3750.50,
    priceChange24h: 2.5,
    volume24h: 15000000000,
    circulatingSupply: 120000000,
    totalSupply: 120000000,
    rank: 2,
    website: 'https://ethereum.org',
    whitepaper: 'https://ethereum.org/en/whitepaper/',
    socialLinks: {
      twitter: 'https://twitter.com/ethereum',
      github: 'https://github.com/ethereum'
    },
    tags: ['Smart Contracts', 'DeFi', 'Layer 1', 'PoS'],
    launchDate: '2015-07-30',
    verified: true,
    communityScore: 95,
    developerScore: 98,
    liquidityScore: 97,
    publicInterestScore: 96
  },
  {
    id: '2',
    name: 'Bitcoin',
    symbol: 'BTC',
    logo: 'https://cryptologos.cc/logos/bitcoin-btc-logo.png',
    description: 'Bitcoin is a decentralized cryptocurrency, originally described in a 2008 whitepaper.',
    category: 'Cryptocurrency',
    marketCap: ************,
    price: 42500.00,
    priceChange24h: -1.2,
    volume24h: 25000000000,
    circulatingSupply: 20000000,
    totalSupply: 21000000,
    rank: 1,
    website: 'https://bitcoin.org',
    whitepaper: 'https://bitcoin.org/bitcoin.pdf',
    socialLinks: {
      github: 'https://github.com/bitcoin'
    },
    tags: ['Store of Value', 'Digital Gold', 'Layer 1', 'PoW'],
    launchDate: '2009-01-03',
    verified: true,
    communityScore: 98,
    developerScore: 95,
    liquidityScore: 99,
    publicInterestScore: 99
  },
  {
    id: '3',
    name: 'Uniswap',
    symbol: 'UNI',
    logo: 'https://cryptologos.cc/logos/uniswap-uni-logo.png',
    description: 'Uniswap is a decentralized cryptocurrency exchange built on Ethereum.',
    category: 'DeFi',
    marketCap: 8500000000,
    price: 14.25,
    priceChange24h: 5.8,
    volume24h: 850000000,
    circulatingSupply: 600000000,
    totalSupply: 1000000000,
    rank: 15,
    website: 'https://uniswap.org',
    whitepaper: 'https://uniswap.org/whitepaper-v3.pdf',
    socialLinks: {
      twitter: 'https://twitter.com/Uniswap',
      github: 'https://github.com/Uniswap'
    },
    tags: ['DEX', 'AMM', 'DeFi', 'Ethereum'],
    launchDate: '2018-11-02',
    verified: true,
    communityScore: 92,
    developerScore: 94,
    liquidityScore: 96,
    publicInterestScore: 88
  },
  {
    id: '4',
    name: 'Solana',
    symbol: 'SOL',
    logo: 'https://cryptologos.cc/logos/solana-sol-logo.png',
    description: 'Solana is a high-performance blockchain platform designed for decentralized applications.',
    category: 'Smart Contract Platform',
    marketCap: 12000000000,
    price: 28.50,
    priceChange24h: -3.1,
    volume24h: 1200000000,
    circulatingSupply: 420000000,
    totalSupply: 500000000,
    rank: 8,
    website: 'https://solana.com',
    whitepaper: 'https://solana.com/solana-whitepaper.pdf',
    socialLinks: {
      twitter: 'https://twitter.com/solana',
      github: 'https://github.com/solana-labs'
    },
    tags: ['Smart Contracts', 'High TPS', 'Layer 1', 'PoH'],
    launchDate: '2020-03-16',
    verified: true,
    communityScore: 88,
    developerScore: 90,
    liquidityScore: 85,
    publicInterestScore: 82
  },
  {
    id: '5',
    name: 'Chainlink',
    symbol: 'LINK',
    logo: 'https://cryptologos.cc/logos/chainlink-link-logo.png',
    description: 'Chainlink is a decentralized oracle network that provides real-world data to smart contracts.',
    category: 'Oracle',
    marketCap: 8500000000,
    price: 15.80,
    priceChange24h: 1.5,
    volume24h: 650000000,
    circulatingSupply: 540000000,
    totalSupply: 1000000000,
    rank: 12,
    website: 'https://chain.link',
    whitepaper: 'https://link.smartcontract.com/whitepaper',
    socialLinks: {
      twitter: 'https://twitter.com/chainlink',
      github: 'https://github.com/smartcontractkit'
    },
    tags: ['Oracle', 'Data Feeds', 'DeFi', 'Cross-chain'],
    launchDate: '2017-09-19',
    verified: true,
    communityScore: 90,
    developerScore: 92,
    liquidityScore: 88,
    publicInterestScore: 85
  }
];

export const mockDiscussions: Discussion[] = [
  {
    id: '1',
    title: 'What are the best DeFi protocols to invest in 2024?',
    content: 'I\'m looking to diversify my DeFi portfolio and would like to know which protocols you think have the most potential this year. I\'m particularly interested in lending protocols and yield farming opportunities.',
    author: mockUsers[0],
    tags: ['DeFi', 'Investment', 'Yield Farming', 'Lending'],
    createdAt: '2024-01-15T10:30:00Z',
    updatedAt: '2024-01-15T10:30:00Z',
    upvotes: 45,
    downvotes: 2,
    replies: [],
    isQuestion: true,
    isAnswered: false,
    views: 234
  },
  {
    id: '2',
    title: 'Ethereum 2.0: Impact on DeFi and gas fees',
    content: 'With the recent developments in Ethereum 2.0, how do you think this will affect DeFi protocols and gas fees? Will we see significant improvements in transaction costs?',
    author: mockUsers[1],
    projectId: '1',
    tags: ['Ethereum', 'DeFi', 'Gas Fees', 'Layer 2'],
    createdAt: '2024-01-14T15:45:00Z',
    updatedAt: '2024-01-14T15:45:00Z',
    upvotes: 32,
    downvotes: 1,
    replies: [],
    isQuestion: true,
    isAnswered: false,
    views: 189
  },
  {
    id: '3',
    title: 'Solana vs Ethereum: Which is better for developers?',
    content: 'I\'m a developer looking to build dApps and I\'m torn between Solana and Ethereum. Solana has lower fees and higher TPS, but Ethereum has a larger ecosystem. What are your thoughts?',
    author: mockUsers[2],
    tags: ['Solana', 'Ethereum', 'Development', 'dApps'],
    createdAt: '2024-01-13T09:20:00Z',
    updatedAt: '2024-01-13T09:20:00Z',
    upvotes: 28,
    downvotes: 5,
    replies: [],
    isQuestion: true,
    isAnswered: false,
    views: 156
  }
];

export const mockMarketData: MarketData = {
  totalMarketCap: 2500000000000,
  totalVolume24h: 85000000000,
  btcDominance: 42.5,
  ethDominance: 18.2,
  marketChange24h: 1.8
};

export const mockTrendingProjects: TrendingProject[] = [
  {
    id: '6',
    name: 'Avalanche',
    symbol: 'AVAX',
    logo: 'https://cryptologos.cc/logos/avalanche-avax-logo.png',
    priceChange24h: 12.5,
    volume24h: 450000000,
    socialScore: 85
  },
  {
    id: '7',
    name: 'Polygon',
    symbol: 'MATIC',
    logo: 'https://cryptologos.cc/logos/polygon-matic-logo.png',
    priceChange24h: 8.9,
    volume24h: 380000000,
    socialScore: 82
  },
  {
    id: '8',
    name: 'Cardano',
    symbol: 'ADA',
    logo: 'https://cryptologos.cc/logos/cardano-ada-logo.png',
    priceChange24h: 6.2,
    volume24h: 320000000,
    socialScore: 78
  }
];
