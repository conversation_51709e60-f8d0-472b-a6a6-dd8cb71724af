import React, { useState } from 'react';
import {
  PenTool,
  Image,
  Link,
  Bold,
  Italic,
  List,
  Send,
  User,
  Clock,
  ThumbsUp,
  MessageSquare,
  Share2,
  Bookmark,
  TrendingUp,
  Eye,
  Hash,
  X
} from 'lucide-react';

interface Post {
  id: string;
  title: string;
  content: string;
  author: {
    address: string;
    displayName?: string;
    avatar?: string;
  };
  timestamp: string;
  tags: string[];
  upvotes: number;
  comments: number;
  views: number;
  isBookmarked?: boolean;
  userVote?: 'up' | 'down' | null;
}

interface UserPostsSectionProps {
  userAddress?: string;
  isConnected: boolean;
}

const UserPostsSection: React.FC<UserPostsSectionProps> = ({ userAddress, isConnected }) => {
  const [posts, setPosts] = useState<Post[]>([
    {
      id: '1',
      title: 'Deep Dive: Why Ethereum 2.0 Will Change Everything',
      content: 'After months of research and analysis, I believe Ethereum 2.0 represents the most significant upgrade in blockchain history. Here\'s why...',
      author: {
        address: '0x1234...5678',
        displayName: 'CryptoResearcher'
      },
      timestamp: '3 hours ago',
      tags: ['ethereum', 'eth2', 'analysis'],
      upvotes: 156,
      comments: 23,
      views: 1240
    },
    {
      id: '2',
      title: 'Market Analysis: Bull Run or Dead Cat Bounce?',
      content: 'Looking at the current market conditions and technical indicators, I see several patterns emerging that suggest...',
      author: {
        address: '0x9876...4321',
        displayName: 'TechnicalTrader'
      },
      timestamp: '6 hours ago',
      tags: ['market-analysis', 'trading', 'bitcoin'],
      upvotes: 89,
      comments: 45,
      views: 892
    }
  ]);

  const [isCreating, setIsCreating] = useState(false);
  const [newPost, setNewPost] = useState({
    title: '',
    content: '',
    tags: [] as string[]
  });
  const [tagInput, setTagInput] = useState('');

  const formatAddress = (address: string): string => {
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  const handleAddTag = () => {
    if (tagInput.trim() && !newPost.tags.includes(tagInput.trim().toLowerCase())) {
      setNewPost(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim().toLowerCase()]
      }));
      setTagInput('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setNewPost(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleSubmitPost = () => {
    if (!newPost.title.trim() || !newPost.content.trim() || !isConnected || !userAddress) return;

    const post: Post = {
      id: Date.now().toString(),
      title: newPost.title,
      content: newPost.content,
      author: {
        address: userAddress,
        displayName: `User${userAddress.slice(-4)}`
      },
      timestamp: 'Just now',
      tags: newPost.tags,
      upvotes: 0,
      comments: 0,
      views: 0
    };

    setPosts(prev => [post, ...prev]);
    setNewPost({ title: '', content: '', tags: [] });
    setIsCreating(false);
  };

  const handleVote = (postId: string) => {
    if (!isConnected) return;

    setPosts(prev => prev.map(post => {
      if (post.id === postId) {
        const isUpvoted = post.userVote === 'up';
        return {
          ...post,
          upvotes: isUpvoted ? post.upvotes - 1 : post.upvotes + 1,
          userVote: isUpvoted ? null : 'up'
        };
      }
      return post;
    }));
  };

  const handleBookmark = (postId: string) => {
    if (!isConnected) return;

    setPosts(prev => prev.map(post => {
      if (post.id === postId) {
        return {
          ...post,
          isBookmarked: !post.isBookmarked
        };
      }
      return post;
    }));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <h2 className="text-2xl font-bold text-gray-900">Community Posts</h2>
          <div className="flex items-center space-x-1 px-3 py-1 bg-blue-100 rounded-full">
            <PenTool className="w-4 h-4 text-blue-600" />
            <span className="text-sm font-medium text-blue-700">Share Insights</span>
          </div>
        </div>

        <button
          onClick={() => setIsCreating(!isCreating)}
          disabled={!isConnected}
          className="btn-primary flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <PenTool className="w-4 h-4" />
          <span>Create Post</span>
        </button>
      </div>

      {/* Create Post Form */}
      {isCreating && (
        <div className="card">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Post Title
              </label>
              <input
                type="text"
                value={newPost.title}
                onChange={(e) => setNewPost(prev => ({ ...prev, title: e.target.value }))}
                placeholder="Enter an engaging title for your post..."
                className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Content
              </label>
              <div className="border border-gray-300 rounded-lg">
                {/* Simple Toolbar */}
                <div className="flex items-center space-x-2 p-2 border-b border-gray-200 bg-gray-50">
                  <button className="p-1 text-gray-600 hover:text-gray-900 rounded">
                    <Bold className="w-4 h-4" />
                  </button>
                  <button className="p-1 text-gray-600 hover:text-gray-900 rounded">
                    <Italic className="w-4 h-4" />
                  </button>
                  <button className="p-1 text-gray-600 hover:text-gray-900 rounded">
                    <List className="w-4 h-4" />
                  </button>
                  <button className="p-1 text-gray-600 hover:text-gray-900 rounded">
                    <Link className="w-4 h-4" />
                  </button>
                  <button className="p-1 text-gray-600 hover:text-gray-900 rounded">
                    <Image className="w-4 h-4" />
                  </button>
                </div>
                <textarea
                  value={newPost.content}
                  onChange={(e) => setNewPost(prev => ({ ...prev, content: e.target.value }))}
                  placeholder="Share your insights, analysis, or thoughts about crypto projects..."
                  className="w-full p-3 border-0 focus:outline-none resize-none"
                  rows={6}
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tags
              </label>
              <div className="flex items-center space-x-2 mb-2">
                <input
                  type="text"
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleAddTag()}
                  placeholder="Add tags (e.g., bitcoin, defi, analysis)"
                  className="flex-1 p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
                <button
                  onClick={handleAddTag}
                  className="px-3 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors"
                >
                  Add
                </button>
              </div>
              {newPost.tags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {newPost.tags.map((tag) => (
                    <span
                      key={tag}
                      className="inline-flex items-center space-x-1 px-2 py-1 bg-primary-100 text-primary-700 rounded-full text-sm"
                    >
                      <Hash className="w-3 h-3" />
                      <span>{tag}</span>
                      <button
                        onClick={() => handleRemoveTag(tag)}
                        className="text-primary-500 hover:text-primary-700"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </span>
                  ))}
                </div>
              )}
            </div>

            <div className="flex items-center justify-between pt-4 border-t border-gray-200">
              <div className="text-sm text-gray-500">
                Posting as {userAddress && formatAddress(userAddress)}
              </div>
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => setIsCreating(false)}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSubmitPost}
                  disabled={!newPost.title.trim() || !newPost.content.trim()}
                  className="btn-primary px-6 py-2 flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Send className="w-4 h-4" />
                  <span>Publish</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Posts List */}
      <div className="space-y-6">
        {posts.map((post) => (
          <div key={post.id} className="card hover:shadow-md transition-shadow">
            {/* Post Header */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                  <User className="w-5 h-5 text-gray-600" />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">
                    {post.author.displayName || formatAddress(post.author.address)}
                  </h3>
                  <div className="flex items-center space-x-2 text-sm text-gray-500">
                    <Clock className="w-3 h-3" />
                    <span>{post.timestamp}</span>
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-2 text-sm text-gray-500">
                <Eye className="w-4 h-4" />
                <span>{post.views}</span>
              </div>
            </div>

            {/* Post Content */}
            <div className="mb-4">
              <h2 className="text-xl font-semibold text-gray-900 mb-2">{post.title}</h2>
              <p className="text-gray-700 line-clamp-3">{post.content}</p>
            </div>

            {/* Tags */}
            {post.tags.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-4">
                {post.tags.map((tag) => (
                  <span
                    key={tag}
                    className="inline-flex items-center space-x-1 px-2 py-1 bg-gray-100 text-gray-600 rounded-full text-xs"
                  >
                    <Hash className="w-3 h-3" />
                    <span>{tag}</span>
                  </span>
                ))}
              </div>
            )}

            {/* Post Actions */}
            <div className="flex items-center justify-between pt-4 border-t border-gray-200">
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => handleVote(post.id)}
                  disabled={!isConnected}
                  className={`flex items-center space-x-1 px-3 py-1 rounded transition-colors ${
                    post.userVote === 'up'
                      ? 'bg-green-100 text-green-700'
                      : 'text-gray-500 hover:text-green-600 hover:bg-green-50'
                  } disabled:opacity-50 disabled:cursor-not-allowed`}
                >
                  <ThumbsUp className="w-4 h-4" />
                  <span className="text-sm">{post.upvotes}</span>
                </button>

                <button className="flex items-center space-x-1 px-3 py-1 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors">
                  <MessageSquare className="w-4 h-4" />
                  <span className="text-sm">{post.comments}</span>
                </button>

                <button className="flex items-center space-x-1 px-3 py-1 text-gray-500 hover:text-gray-700 hover:bg-gray-50 rounded transition-colors">
                  <Share2 className="w-4 h-4" />
                  <span className="text-sm">Share</span>
                </button>
              </div>

              <button
                onClick={() => handleBookmark(post.id)}
                disabled={!isConnected}
                className={`p-2 rounded transition-colors ${
                  post.isBookmarked
                    ? 'text-yellow-600 bg-yellow-50'
                    : 'text-gray-400 hover:text-yellow-600 hover:bg-yellow-50'
                } disabled:opacity-50 disabled:cursor-not-allowed`}
              >
                <Bookmark className="w-4 h-4" />
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Load More */}
      <div className="text-center">
        <button className="btn-secondary px-6 py-2">
          Load More Posts
        </button>
      </div>
    </div>
  );
};

export default UserPostsSection;
