import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import {
  TrendingUp,
  TrendingDown,
  ExternalLink,
  Shield,
  Users,
  Code,
  BarChart3,
  Star,
  Heart,
  Eye,
  ArrowUpRight,
  ArrowDownRight,
  Activity,
  Globe,
  Github,
  Twitter,
  MessageCircle,
  Bookmark,
  BookMarked as BookmarkCheck
} from 'lucide-react';
import { CryptoProject } from '../types';

interface ProjectCardProps {
  project: CryptoProject;
  showRank?: boolean;
  variant?: 'default' | 'compact' | 'featured';
  showQuickActions?: boolean;
}

const ProjectCard: React.FC<ProjectCardProps> = ({
  project,
  showRank = true,
  variant = 'default',
  showQuickActions = true
}) => {
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [isWatching, setIsWatching] = useState(false);
  const formatNumber = (num: number): string => {
    if (num >= 1e9) return (num / 1e9).toFixed(2) + 'B';
    if (num >= 1e6) return (num / 1e6).toFixed(2) + 'M';
    if (num >= 1e3) return (num / 1e3).toFixed(2) + 'K';
    return num.toString();
  };

  const formatPrice = (price: number): string => {
    if (price >= 1) return price.toFixed(2);
    if (price >= 0.01) return price.toFixed(4);
    return price.toFixed(8);
  };

  const getScoreColor = (score: number): string => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBgColor = (score: number): string => {
    if (score >= 90) return 'bg-green-100';
    if (score >= 70) return 'bg-yellow-100';
    return 'bg-red-100';
  };

  const getPriceChangeIcon = (change: number) => {
    return change >= 0 ? (
      <ArrowUpRight className="w-4 h-4 text-green-500" />
    ) : (
      <ArrowDownRight className="w-4 h-4 text-red-500" />
    );
  };

  const handleBookmark = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsBookmarked(!isBookmarked);
  };

  const handleWatch = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsWatching(!isWatching);
  };

  if (variant === 'compact') {
    return (
      <div className="card hover:shadow-lg transition-all duration-300 hover:-translate-y-1 bg-gradient-to-br from-white to-gray-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <img
                src={project.logo}
                alt={project.name}
                className="w-10 h-10 rounded-lg"
                onError={(e) => {
                  e.currentTarget.src = 'https://via.placeholder.com/40x40/6366f1/ffffff?text=' + project.symbol.charAt(0);
                }}
              />
              {project.verified && (
                <Shield className="w-3 h-3 text-blue-500 absolute -top-1 -right-1 bg-white rounded-full" />
              )}
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">{project.name}</h3>
              <p className="text-sm text-gray-500">{project.symbol}</p>
            </div>
          </div>
          <div className="text-right">
            <p className="font-bold text-gray-900">${formatPrice(project.price)}</p>
            <div className="flex items-center justify-end space-x-1">
              {getPriceChangeIcon(project.priceChange24h)}
              <span className={`text-sm font-medium ${
                project.priceChange24h >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {Math.abs(project.priceChange24h).toFixed(2)}%
              </span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="card hover:shadow-xl transition-all duration-300 hover:-translate-y-2 bg-gradient-to-br from-white to-gray-50 group relative overflow-hidden">
      {/* Gradient overlay for featured variant */}
      {variant === 'featured' && (
        <div className="absolute inset-0 bg-gradient-to-r from-primary-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      )}

      {/* Quick Actions */}
      {showQuickActions && (
        <div className="absolute top-4 right-4 flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <button
            onClick={handleBookmark}
            className="p-2 bg-white/90 backdrop-blur-sm rounded-lg shadow-sm hover:bg-white transition-colors"
          >
            {isBookmarked ? (
              <BookmarkCheck className="w-4 h-4 text-primary-600" />
            ) : (
              <Bookmark className="w-4 h-4 text-gray-600" />
            )}
          </button>
          <button
            onClick={handleWatch}
            className="p-2 bg-white/90 backdrop-blur-sm rounded-lg shadow-sm hover:bg-white transition-colors"
          >
            <Eye className={`w-4 h-4 ${isWatching ? 'text-primary-600' : 'text-gray-600'}`} />
          </button>
        </div>
      )}

      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="relative">
            <img
              src={project.logo}
              alt={project.name}
              className="w-12 h-12 rounded-lg shadow-sm"
              onError={(e) => {
                e.currentTarget.src = 'https://via.placeholder.com/48x48/6366f1/ffffff?text=' + project.symbol.charAt(0);
              }}
            />
            {project.verified && (
              <div className="absolute -top-1 -right-1 w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                <Shield className="w-3 h-3 text-white" />
              </div>
            )}
          </div>
          <div>
            <div className="flex items-center space-x-2">
              <h3 className="text-lg font-semibold text-gray-900 group-hover:text-primary-600 transition-colors">
                {project.name}
              </h3>
              {showRank && (
                <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-600 rounded-full">
                  #{project.rank}
                </span>
              )}
            </div>
            <div className="flex items-center space-x-2 mt-1">
              <p className="text-sm text-gray-500">{project.symbol}</p>
              <span className="text-xs text-gray-400">•</span>
              <p className="text-sm text-gray-500">{project.category}</p>
            </div>
          </div>
        </div>

        {showRank && (
          <div className="flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full">
            <span className="text-sm font-medium text-gray-700">#{project.rank}</span>
          </div>
        )}
      </div>

      {/* Price and Change */}
      <div className="mb-4">
        <div className="flex items-center justify-between mb-2">
          <span className="text-2xl font-bold text-gray-900">
            ${formatPrice(project.price)}
          </span>
          <div className={`flex items-center space-x-1 ${
            project.priceChange24h >= 0 ? 'text-green-600' : 'text-red-600'
          }`}>
            {project.priceChange24h >= 0 ? (
              <TrendingUp className="w-4 h-4" />
            ) : (
              <TrendingDown className="w-4 h-4" />
            )}
            <span className="font-medium">
              {project.priceChange24h >= 0 ? '+' : ''}{project.priceChange24h.toFixed(2)}%
            </span>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <p className="text-gray-500">Market Cap</p>
            <p className="font-medium">${formatNumber(project.marketCap)}</p>
          </div>
          <div>
            <p className="text-gray-500">Volume 24h</p>
            <p className="font-medium">${formatNumber(project.volume24h)}</p>
          </div>
        </div>
      </div>

      {/* Description */}
      <p className="text-gray-600 text-sm mb-4 line-clamp-2">
        {project.description}
      </p>

      {/* Tags */}
      <div className="flex flex-wrap gap-2 mb-4">
        {project.tags.slice(0, 3).map((tag) => (
          <span key={tag} className="badge badge-info text-xs">
            {tag}
          </span>
        ))}
        {project.tags.length > 3 && (
          <span className="text-xs text-gray-500">+{project.tags.length - 3} more</span>
        )}
      </div>

      {/* Enhanced Scores with Visual Indicators */}
      <div className="grid grid-cols-2 gap-3 mb-4">
        <div className={`p-3 rounded-lg ${getScoreBgColor(project.communityScore)} border`}>
          <div className="flex items-center space-x-2 mb-1">
            <Users className="w-4 h-4 text-blue-500" />
            <p className="text-xs font-medium text-gray-700">Community</p>
          </div>
          <div className="flex items-center justify-between">
            <p className={`text-lg font-bold ${getScoreColor(project.communityScore)}`}>
              {project.communityScore}
            </p>
            <div className="flex items-center space-x-1">
              {Array.from({ length: 5 }).map((_, i) => (
                <Star
                  key={i}
                  className={`w-3 h-3 ${
                    i < Math.floor(project.communityScore / 20)
                      ? 'text-yellow-400 fill-current'
                      : 'text-gray-300'
                  }`}
                />
              ))}
            </div>
          </div>
        </div>

        <div className={`p-3 rounded-lg ${getScoreBgColor(project.developerScore)} border`}>
          <div className="flex items-center space-x-2 mb-1">
            <Code className="w-4 h-4 text-purple-500" />
            <p className="text-xs font-medium text-gray-700">Developer</p>
          </div>
          <div className="flex items-center justify-between">
            <p className={`text-lg font-bold ${getScoreColor(project.developerScore)}`}>
              {project.developerScore}
            </p>
            <div className="flex items-center space-x-1">
              {Array.from({ length: 5 }).map((_, i) => (
                <Star
                  key={i}
                  className={`w-3 h-3 ${
                    i < Math.floor(project.developerScore / 20)
                      ? 'text-yellow-400 fill-current'
                      : 'text-gray-300'
                  }`}
                />
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Social Proof & Activity */}
      <div className="flex items-center justify-between mb-4 p-3 bg-gray-50 rounded-lg">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-1">
            <Activity className="w-4 h-4 text-green-500" />
            <span className="text-sm font-medium text-gray-700">Active</span>
          </div>
          <div className="flex items-center space-x-1">
            <MessageCircle className="w-4 h-4 text-blue-500" />
            <span className="text-sm text-gray-600">1.2k</span>
          </div>
          <div className="flex items-center space-x-1">
            <Heart className="w-4 h-4 text-red-500" />
            <span className="text-sm text-gray-600">856</span>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {project.socialLinks.github && (
            <a
              href={project.socialLinks.github}
              target="_blank"
              rel="noopener noreferrer"
              className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <Github className="w-4 h-4" />
            </a>
          )}
          {project.socialLinks.twitter && (
            <a
              href={project.socialLinks.twitter}
              target="_blank"
              rel="noopener noreferrer"
              className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <Twitter className="w-4 h-4" />
            </a>
          )}
        </div>
      </div>

      {/* Enhanced Actions */}
      <div className="flex items-center justify-between pt-4 border-t border-gray-200">
        <Link
          to={`/projects/${project.id}`}
          className="btn-primary text-sm px-6 py-2 flex items-center space-x-2 group"
        >
          <span>View Details</span>
          <ArrowUpRight className="w-4 h-4 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform" />
        </Link>

        <div className="flex space-x-2">
          {project.website && (
            <a
              href={project.website}
              target="_blank"
              rel="noopener noreferrer"
              className="p-2 text-gray-400 hover:text-primary-600 transition-colors rounded-lg hover:bg-primary-50"
              title="Visit Website"
            >
              <Globe className="w-4 h-4" />
            </a>
          )}
          {project.whitepaper && (
            <a
              href={project.whitepaper}
              target="_blank"
              rel="noopener noreferrer"
              className="p-2 text-gray-400 hover:text-primary-600 transition-colors rounded-lg hover:bg-primary-50"
              title="View Whitepaper"
            >
              <BarChart3 className="w-4 h-4" />
            </a>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProjectCard;
