import React from 'react';
import { Link } from 'react-router-dom';
import { TrendingUp, TrendingDown, ExternalLink, Shield, Users, Code, BarChart3 } from 'lucide-react';
import { CryptoProject } from '../types';

interface ProjectCardProps {
  project: CryptoProject;
  showRank?: boolean;
}

const ProjectCard: React.FC<ProjectCardProps> = ({ project, showRank = true }) => {
  const formatNumber = (num: number): string => {
    if (num >= 1e9) return (num / 1e9).toFixed(2) + 'B';
    if (num >= 1e6) return (num / 1e6).toFixed(2) + 'M';
    if (num >= 1e3) return (num / 1e3).toFixed(2) + 'K';
    return num.toString();
  };

  const formatPrice = (price: number): string => {
    if (price >= 1) return price.toFixed(2);
    if (price >= 0.01) return price.toFixed(4);
    return price.toFixed(8);
  };

  const getScoreColor = (score: number): string => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="card hover:shadow-md transition-shadow duration-200">
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <img
            src={project.logo}
            alt={project.name}
            className="w-12 h-12 rounded-lg"
            onError={(e) => {
              e.currentTarget.src = 'https://via.placeholder.com/48x48/6366f1/ffffff?text=' + project.symbol.charAt(0);
            }}
          />
          <div>
            <div className="flex items-center space-x-2">
              <h3 className="text-lg font-semibold text-gray-900">{project.name}</h3>
              {project.verified && (
                <Shield className="w-4 h-4 text-blue-500" />
              )}
            </div>
            <p className="text-sm text-gray-500">{project.symbol}</p>
          </div>
        </div>

        {showRank && (
          <div className="flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full">
            <span className="text-sm font-medium text-gray-700">#{project.rank}</span>
          </div>
        )}
      </div>

      {/* Price and Change */}
      <div className="mb-4">
        <div className="flex items-center justify-between mb-2">
          <span className="text-2xl font-bold text-gray-900">
            ${formatPrice(project.price)}
          </span>
          <div className={`flex items-center space-x-1 ${
            project.priceChange24h >= 0 ? 'text-green-600' : 'text-red-600'
          }`}>
            {project.priceChange24h >= 0 ? (
              <TrendingUp className="w-4 h-4" />
            ) : (
              <TrendingDown className="w-4 h-4" />
            )}
            <span className="font-medium">
              {project.priceChange24h >= 0 ? '+' : ''}{project.priceChange24h.toFixed(2)}%
            </span>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <p className="text-gray-500">Market Cap</p>
            <p className="font-medium">${formatNumber(project.marketCap)}</p>
          </div>
          <div>
            <p className="text-gray-500">Volume 24h</p>
            <p className="font-medium">${formatNumber(project.volume24h)}</p>
          </div>
        </div>
      </div>

      {/* Description */}
      <p className="text-gray-600 text-sm mb-4 line-clamp-2">
        {project.description}
      </p>

      {/* Tags */}
      <div className="flex flex-wrap gap-2 mb-4">
        {project.tags.slice(0, 3).map((tag) => (
          <span key={tag} className="badge badge-info text-xs">
            {tag}
          </span>
        ))}
        {project.tags.length > 3 && (
          <span className="text-xs text-gray-500">+{project.tags.length - 3} more</span>
        )}
      </div>

      {/* Scores */}
      <div className="grid grid-cols-2 gap-3 mb-4">
        <div className="flex items-center space-x-2">
          <Users className="w-4 h-4 text-blue-500" />
          <div>
            <p className="text-xs text-gray-500">Community</p>
            <p className={`text-sm font-medium ${getScoreColor(project.communityScore)}`}>
              {project.communityScore}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Code className="w-4 h-4 text-purple-500" />
          <div>
            <p className="text-xs text-gray-500">Developer</p>
            <p className={`text-sm font-medium ${getScoreColor(project.developerScore)}`}>
              {project.developerScore}
            </p>
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-center justify-between pt-4 border-t border-gray-200">
        <Link
          to={`/projects/${project.id}`}
          className="btn-primary text-sm px-4 py-2"
        >
          View Details
        </Link>

        <div className="flex space-x-2">
          {project.website && (
            <a
              href={project.website}
              target="_blank"
              rel="noopener noreferrer"
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <ExternalLink className="w-4 h-4" />
            </a>
          )}
          {project.whitepaper && (
            <a
              href={project.whitepaper}
              target="_blank"
              rel="noopener noreferrer"
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <BarChart3 className="w-4 h-4" />
            </a>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProjectCard;
