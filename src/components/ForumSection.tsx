import React, { useState } from 'react';
import {
  MessageSquare,
  ThumbsUp,
  ThumbsDown,
  Reply,
  Send,
  User,
  Clock,
  TrendingUp,
  Flame,
  Star,
  Shield,
  MoreHorizontal
} from 'lucide-react';

interface Comment {
  id: string;
  content: string;
  author: {
    address: string;
    displayName?: string;
    avatar?: string;
    isVerified?: boolean;
  };
  timestamp: string;
  upvotes: number;
  downvotes: number;
  replies: Comment[];
  userVote?: 'up' | 'down' | null;
}

interface ForumSectionProps {
  userAddress?: string;
  isConnected: boolean;
}

const ForumSection: React.FC<ForumSectionProps> = ({ userAddress, isConnected }) => {
  const [comments, setComments] = useState<Comment[]>([
    {
      id: '1',
      content: 'What do you think about the recent Bitcoin price movement? I\'m seeing some interesting patterns in the charts.',
      author: {
        address: '0x1234...5678',
        displayName: 'CryptoAnalyst',
        isVerified: true
      },
      timestamp: '2 hours ago',
      upvotes: 24,
      downvotes: 3,
      replies: [
        {
          id: '1-1',
          content: 'I agree! The support level at $42k seems to be holding strong. Could be a good entry point.',
          author: {
            address: '0x9876...4321',
            displayName: 'TraderJoe'
          },
          timestamp: '1 hour ago',
          upvotes: 12,
          downvotes: 1,
          replies: []
        }
      ]
    },
    {
      id: '2',
      content: 'Just discovered this new DeFi protocol. The APY looks too good to be true. Has anyone done their research on this?',
      author: {
        address: '0xabcd...efgh',
        displayName: 'DeFiExplorer'
      },
      timestamp: '4 hours ago',
      upvotes: 18,
      downvotes: 7,
      replies: []
    }
  ]);

  const [newComment, setNewComment] = useState('');
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyContent, setReplyContent] = useState('');
  const [sortBy, setSortBy] = useState<'recent' | 'popular' | 'trending'>('recent');

  const formatAddress = (address: string): string => {
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  const handleVote = (commentId: string, voteType: 'up' | 'down') => {
    if (!isConnected) return;

    setComments(prev => prev.map(comment => {
      if (comment.id === commentId) {
        const currentVote = comment.userVote;
        let newUpvotes = comment.upvotes;
        let newDownvotes = comment.downvotes;
        let newUserVote: 'up' | 'down' | null = voteType;

        // Remove previous vote
        if (currentVote === 'up') newUpvotes--;
        if (currentVote === 'down') newDownvotes--;

        // Add new vote or remove if same
        if (currentVote === voteType) {
          newUserVote = null;
        } else {
          if (voteType === 'up') newUpvotes++;
          if (voteType === 'down') newDownvotes++;
        }

        return {
          ...comment,
          upvotes: newUpvotes,
          downvotes: newDownvotes,
          userVote: newUserVote
        };
      }
      return comment;
    }));
  };

  const handleSubmitComment = () => {
    if (!newComment.trim() || !isConnected || !userAddress) return;

    const comment: Comment = {
      id: Date.now().toString(),
      content: newComment,
      author: {
        address: userAddress,
        displayName: `User${userAddress.slice(-4)}`
      },
      timestamp: 'Just now',
      upvotes: 0,
      downvotes: 0,
      replies: []
    };

    setComments(prev => [comment, ...prev]);
    setNewComment('');
  };

  const handleSubmitReply = (parentId: string) => {
    if (!replyContent.trim() || !isConnected || !userAddress) return;

    const reply: Comment = {
      id: `${parentId}-${Date.now()}`,
      content: replyContent,
      author: {
        address: userAddress,
        displayName: `User${userAddress.slice(-4)}`
      },
      timestamp: 'Just now',
      upvotes: 0,
      downvotes: 0,
      replies: []
    };

    setComments(prev => prev.map(comment => {
      if (comment.id === parentId) {
        return {
          ...comment,
          replies: [...comment.replies, reply]
        };
      }
      return comment;
    }));

    setReplyContent('');
    setReplyingTo(null);
  };

  const getSortIcon = (type: string) => {
    switch (type) {
      case 'popular': return <ThumbsUp className="w-4 h-4" />;
      case 'trending': return <TrendingUp className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <h2 className="text-2xl font-bold text-gray-900">Community Forum</h2>
          <div className="flex items-center space-x-1 px-3 py-1 bg-orange-100 rounded-full">
            <Flame className="w-4 h-4 text-orange-600" />
            <span className="text-sm font-medium text-orange-700">Live</span>
          </div>
        </div>

        {/* Sort Options */}
        <div className="flex items-center space-x-2">
          {(['recent', 'popular', 'trending'] as const).map((option) => (
            <button
              key={option}
              onClick={() => setSortBy(option)}
              className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                sortBy === option
                  ? 'bg-primary-100 text-primary-700'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              }`}
            >
              {getSortIcon(option)}
              <span className="capitalize">{option}</span>
            </button>
          ))}
        </div>
      </div>

      {/* New Comment */}
      <div className="card">
        <div className="flex items-start space-x-4">
          <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
            <User className="w-5 h-5 text-primary-600" />
          </div>
          <div className="flex-1">
            <textarea
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              placeholder={isConnected ? "Share your thoughts with the community..." : "Connect your wallet to join the discussion"}
              disabled={!isConnected}
              className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
              rows={3}
            />
            <div className="flex items-center justify-between mt-3">
              <div className="text-sm text-gray-500">
                {isConnected ? (
                  <span>Posting as {userAddress && formatAddress(userAddress)}</span>
                ) : (
                  <span>Connect wallet to participate</span>
                )}
              </div>
              <button
                onClick={handleSubmitComment}
                disabled={!newComment.trim() || !isConnected}
                className="btn-primary px-4 py-2 text-sm disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
              >
                <Send className="w-4 h-4" />
                <span>Post</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Comments */}
      <div className="space-y-4">
        {comments.map((comment) => (
          <div key={comment.id} className="card">
            {/* Comment Header */}
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                  <User className="w-4 h-4 text-gray-600" />
                </div>
                <div>
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-gray-900">
                      {comment.author.displayName || formatAddress(comment.author.address)}
                    </span>
                    {comment.author.isVerified && (
                      <Shield className="w-4 h-4 text-blue-500" />
                    )}
                  </div>
                  <div className="flex items-center space-x-2 text-sm text-gray-500">
                    <Clock className="w-3 h-3" />
                    <span>{comment.timestamp}</span>
                  </div>
                </div>
              </div>
              <button className="p-1 text-gray-400 hover:text-gray-600 rounded">
                <MoreHorizontal className="w-4 h-4" />
              </button>
            </div>

            {/* Comment Content */}
            <p className="text-gray-700 mb-4">{comment.content}</p>

            {/* Comment Actions */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => handleVote(comment.id, 'up')}
                  disabled={!isConnected}
                  className={`flex items-center space-x-1 px-2 py-1 rounded transition-colors ${
                    comment.userVote === 'up'
                      ? 'bg-green-100 text-green-700'
                      : 'text-gray-500 hover:text-green-600 hover:bg-green-50'
                  } disabled:opacity-50 disabled:cursor-not-allowed`}
                >
                  <ThumbsUp className="w-4 h-4" />
                  <span className="text-sm">{comment.upvotes}</span>
                </button>

                <button
                  onClick={() => handleVote(comment.id, 'down')}
                  disabled={!isConnected}
                  className={`flex items-center space-x-1 px-2 py-1 rounded transition-colors ${
                    comment.userVote === 'down'
                      ? 'bg-red-100 text-red-700'
                      : 'text-gray-500 hover:text-red-600 hover:bg-red-50'
                  } disabled:opacity-50 disabled:cursor-not-allowed`}
                >
                  <ThumbsDown className="w-4 h-4" />
                  <span className="text-sm">{comment.downvotes}</span>
                </button>

                <button
                  onClick={() => setReplyingTo(replyingTo === comment.id ? null : comment.id)}
                  disabled={!isConnected}
                  className="flex items-center space-x-1 px-2 py-1 text-gray-500 hover:text-primary-600 hover:bg-primary-50 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Reply className="w-4 h-4" />
                  <span className="text-sm">Reply</span>
                </button>
              </div>

              {comment.replies.length > 0 && (
                <div className="flex items-center space-x-1 text-sm text-gray-500">
                  <MessageSquare className="w-4 h-4" />
                  <span>{comment.replies.length} replies</span>
                </div>
              )}
            </div>

            {/* Reply Form */}
            {replyingTo === comment.id && (
              <div className="mt-4 pl-8 border-l-2 border-gray-200">
                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                    <User className="w-4 h-4 text-primary-600" />
                  </div>
                  <div className="flex-1">
                    <textarea
                      value={replyContent}
                      onChange={(e) => setReplyContent(e.target.value)}
                      placeholder="Write a reply..."
                      className="w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
                      rows={2}
                    />
                    <div className="flex items-center justify-end space-x-2 mt-2">
                      <button
                        onClick={() => setReplyingTo(null)}
                        className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800"
                      >
                        Cancel
                      </button>
                      <button
                        onClick={() => handleSubmitReply(comment.id)}
                        disabled={!replyContent.trim()}
                        className="btn-primary px-3 py-1 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Reply
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Replies */}
            {comment.replies.length > 0 && (
              <div className="mt-4 pl-8 border-l-2 border-gray-200 space-y-3">
                {comment.replies.map((reply) => (
                  <div key={reply.id} className="bg-gray-50 rounded-lg p-3">
                    <div className="flex items-center space-x-2 mb-2">
                      <div className="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center">
                        <User className="w-3 h-3 text-gray-600" />
                      </div>
                      <span className="font-medium text-sm text-gray-900">
                        {reply.author.displayName || formatAddress(reply.author.address)}
                      </span>
                      <span className="text-xs text-gray-500">{reply.timestamp}</span>
                    </div>
                    <p className="text-sm text-gray-700 mb-2">{reply.content}</p>
                    <div className="flex items-center space-x-3">
                      <button className="flex items-center space-x-1 text-xs text-gray-500 hover:text-green-600">
                        <ThumbsUp className="w-3 h-3" />
                        <span>{reply.upvotes}</span>
                      </button>
                      <button className="flex items-center space-x-1 text-xs text-gray-500 hover:text-red-600">
                        <ThumbsDown className="w-3 h-3" />
                        <span>{reply.downvotes}</span>
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default ForumSection;
