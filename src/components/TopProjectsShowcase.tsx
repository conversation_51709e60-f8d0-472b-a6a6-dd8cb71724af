import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import {
  TrendingUp,
  TrendingDown,
  Filter,
  Grid,
  List,
  ChevronLeft,
  ChevronRight,
  Star,
  Award,
  Crown,
  Medal,
  ArrowUpRight,
  BarChart3,
  Users,
  Activity
} from 'lucide-react';
import { CryptoProject } from '../types';
import ProjectCard from './ProjectCard';

interface TopProjectsShowcaseProps {
  projects: CryptoProject[];
  title?: string;
  showFilters?: boolean;
  variant?: 'grid' | 'carousel' | 'list';
  className?: string;
}

const TopProjectsShowcase: React.FC<TopProjectsShowcaseProps> = ({
  projects,
  title = "Top Projects",
  showFilters = true,
  variant = 'grid',
  className = ''
}) => {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>(variant === 'list' ? 'list' : 'grid');
  const [filterBy, setFilterBy] = useState<'rank' | 'price_change' | 'volume' | 'market_cap'>('rank');
  const [currentSlide, setCurrentSlide] = useState(0);

  const formatNumber = (num: number): string => {
    if (num >= 1e12) return (num / 1e12).toFixed(2) + 'T';
    if (num >= 1e9) return (num / 1e9).toFixed(2) + 'B';
    if (num >= 1e6) return (num / 1e6).toFixed(2) + 'M';
    if (num >= 1e3) return (num / 1e3).toFixed(2) + 'K';
    return num.toString();
  };

  const formatPrice = (price: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(price);
  };

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Crown className="w-5 h-5 text-yellow-500" />;
      case 2:
        return <Medal className="w-5 h-5 text-gray-400" />;
      case 3:
        return <Award className="w-5 h-5 text-amber-600" />;
      default:
        return <span className="text-sm font-bold text-gray-600">#{rank}</span>;
    }
  };

  const sortedProjects = [...projects].sort((a, b) => {
    switch (filterBy) {
      case 'price_change':
        return b.priceChange24h - a.priceChange24h;
      case 'volume':
        return b.volume24h - a.volume24h;
      case 'market_cap':
        return b.marketCap - a.marketCap;
      default:
        return a.rank - b.rank;
    }
  });

  const itemsPerSlide = 3;
  const maxSlides = Math.ceil(sortedProjects.length / itemsPerSlide);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % maxSlides);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + maxSlides) % maxSlides);
  };

  const getVisibleProjects = () => {
    if (variant === 'carousel') {
      const start = currentSlide * itemsPerSlide;
      return sortedProjects.slice(start, start + itemsPerSlide);
    }
    return sortedProjects.slice(0, variant === 'list' ? 10 : 6);
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <h2 className="text-2xl font-bold text-gray-900">{title}</h2>
          <div className="flex items-center space-x-1 px-3 py-1 bg-primary-100 rounded-full">
            <TrendingUp className="w-4 h-4 text-primary-600" />
            <span className="text-sm font-medium text-primary-700">Live</span>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          {showFilters && (
            <>
              {/* Filter Dropdown */}
              <div className="relative">
                <select
                  value={filterBy}
                  onChange={(e) => setFilterBy(e.target.value as any)}
                  className="appearance-none bg-white border border-gray-300 rounded-lg px-4 py-2 pr-8 text-sm font-medium text-gray-700 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="rank">By Rank</option>
                  <option value="price_change">By Price Change</option>
                  <option value="volume">By Volume</option>
                  <option value="market_cap">By Market Cap</option>
                </select>
                <Filter className="w-4 h-4 text-gray-400 absolute right-2 top-1/2 transform -translate-y-1/2 pointer-events-none" />
              </div>

              {/* View Mode Toggle */}
              {variant !== 'carousel' && (
                <div className="flex items-center bg-gray-100 rounded-lg p-1">
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`p-2 rounded-md transition-colors ${
                      viewMode === 'grid'
                        ? 'bg-white text-primary-600 shadow-sm'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    <Grid className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => setViewMode('list')}
                    className={`p-2 rounded-md transition-colors ${
                      viewMode === 'list'
                        ? 'bg-white text-primary-600 shadow-sm'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    <List className="w-4 h-4" />
                  </button>
                </div>
              )}
            </>
          )}

          {/* Carousel Controls */}
          {variant === 'carousel' && (
            <div className="flex items-center space-x-2">
              <button
                onClick={prevSlide}
                className="p-2 rounded-lg border border-gray-300 hover:bg-gray-50 transition-colors"
              >
                <ChevronLeft className="w-4 h-4" />
              </button>
              <span className="text-sm text-gray-500">
                {currentSlide + 1} / {maxSlides}
              </span>
              <button
                onClick={nextSlide}
                className="p-2 rounded-lg border border-gray-300 hover:bg-gray-50 transition-colors"
              >
                <ChevronRight className="w-4 h-4" />
              </button>
            </div>
          )}

          <Link
            to="/projects"
            className="text-primary-600 hover:text-primary-700 font-medium text-sm flex items-center space-x-1"
          >
            <span>View All</span>
            <ArrowUpRight className="w-4 h-4" />
          </Link>
        </div>
      </div>

      {/* Top 3 Podium (for rank filter) */}
      {filterBy === 'rank' && sortedProjects.length >= 3 && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {/* 2nd Place */}
          <div className="order-1 md:order-1">
            <div className="card bg-gradient-to-br from-gray-50 to-gray-100 border-2 border-gray-300 relative">
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <div className="w-8 h-8 bg-gray-400 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-sm">2</span>
                </div>
              </div>
              <div className="pt-4">
                <ProjectCard project={sortedProjects[1]} variant="compact" showQuickActions={false} />
              </div>
            </div>
          </div>

          {/* 1st Place */}
          <div className="order-2 md:order-2">
            <div className="card bg-gradient-to-br from-yellow-50 to-yellow-100 border-2 border-yellow-300 relative transform md:scale-105">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <div className="w-10 h-10 bg-yellow-500 rounded-full flex items-center justify-center">
                  <Crown className="w-5 h-5 text-white" />
                </div>
              </div>
              <div className="pt-6">
                <ProjectCard project={sortedProjects[0]} variant="compact" showQuickActions={false} />
              </div>
            </div>
          </div>

          {/* 3rd Place */}
          <div className="order-3 md:order-3">
            <div className="card bg-gradient-to-br from-amber-50 to-amber-100 border-2 border-amber-300 relative">
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <div className="w-8 h-8 bg-amber-600 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-sm">3</span>
                </div>
              </div>
              <div className="pt-4">
                <ProjectCard project={sortedProjects[2]} variant="compact" showQuickActions={false} />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Projects Grid/List */}
      {viewMode === 'grid' ? (
        <div className={`grid gap-6 ${
          variant === 'carousel'
            ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
            : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
        }`}>
          {getVisibleProjects().map((project, index) => (
            <div key={project.id} className="relative">
              {filterBy === 'rank' && index < 3 && (
                <div className="absolute -top-2 -left-2 z-10">
                  {getRankIcon(project.rank)}
                </div>
              )}
              <ProjectCard
                project={project}
                variant={variant === 'carousel' ? 'featured' : 'default'}
                showRank={filterBy === 'rank'}
              />
            </div>
          ))}
        </div>
      ) : (
        <div className="space-y-4">
          {getVisibleProjects().map((project, index) => (
            <div key={project.id} className="card hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center justify-center w-8 h-8">
                    {getRankIcon(project.rank)}
                  </div>
                  <ProjectCard project={project} variant="compact" showQuickActions={false} />
                </div>
                <div className="flex items-center space-x-6 text-sm">
                  <div className="text-right">
                    <p className="font-medium text-gray-900">${formatNumber(project.marketCap)}</p>
                    <p className="text-gray-500">Market Cap</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-gray-900">${formatNumber(project.volume24h)}</p>
                    <p className="text-gray-500">Volume 24h</p>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center space-x-1">
                      <Activity className="w-4 h-4 text-green-500" />
                      <span className="font-medium text-gray-900">{project.communityScore}</span>
                    </div>
                    <p className="text-gray-500">Community</p>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Carousel Indicators */}
      {variant === 'carousel' && maxSlides > 1 && (
        <div className="flex justify-center space-x-2">
          {Array.from({ length: maxSlides }).map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentSlide(index)}
              className={`w-2 h-2 rounded-full transition-colors ${
                index === currentSlide ? 'bg-primary-600' : 'bg-gray-300'
              }`}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default TopProjectsShowcase;
