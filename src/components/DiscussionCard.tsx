import React from 'react';
import { Link } from 'react-router-dom';
import {
  MessageSquare,
  ThumbsUp,
  ThumbsDown,
  Eye,
  Clock,
  Tag,
  CheckCircle,
  User,
  Shield
} from 'lucide-react';
import { Discussion } from '../types';

interface DiscussionCardProps {
  discussion: Discussion;
}

const DiscussionCard: React.FC<DiscussionCardProps> = ({ discussion }) => {
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`;
    return date.toLocaleDateString();
  };

  const formatNumber = (num: number): string => {
    if (num >= 1000) return (num / 1000).toFixed(1) + 'k';
    return num.toString();
  };

  return (
    <div className="card hover:shadow-md transition-shadow duration-200">
      {/* Header */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2">
            {discussion.isQuestion ? (
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <MessageSquare className="w-4 h-4 text-blue-600" />
              </div>
            ) : (
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <MessageSquare className="w-4 h-4 text-green-600" />
              </div>
            )}

            {discussion.isAnswered && (
              <CheckCircle className="w-5 h-5 text-green-500" />
            )}
          </div>

          <div>
            <Link
              to={`/discussion/${discussion.id}`}
              className="text-lg font-semibold text-gray-900 hover:text-primary-600 transition-colors line-clamp-2"
            >
              {discussion.title}
            </Link>

            <div className="flex items-center space-x-4 mt-1 text-sm text-gray-500">
              <div className="flex items-center space-x-1">
                <User className="w-4 h-4" />
                <span className="flex items-center space-x-1">
                  {discussion.author.username}
                  {discussion.author.isVerified && (
                    <Shield className="w-3 h-3 text-blue-500" />
                  )}
                </span>
              </div>

              <div className="flex items-center space-x-1">
                <Clock className="w-4 h-4" />
                <span>{formatDate(discussion.createdAt)}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content Preview */}
      <p className="text-gray-600 text-sm mb-4 line-clamp-3">
        {discussion.content}
      </p>

      {/* Tags */}
      <div className="flex flex-wrap gap-2 mb-4">
        {discussion.tags.map((tag) => (
          <span key={tag} className="badge badge-info text-xs flex items-center space-x-1">
            <Tag className="w-3 h-3" />
            <span>{tag}</span>
          </span>
        ))}
      </div>

      {/* Stats and Actions */}
      <div className="flex items-center justify-between pt-4 border-t border-gray-200">
        <div className="flex items-center space-x-6 text-sm text-gray-500">
          <div className="flex items-center space-x-1">
            <ThumbsUp className="w-4 h-4" />
            <span>{formatNumber(discussion.upvotes)}</span>
          </div>

          <div className="flex items-center space-x-1">
            <ThumbsDown className="w-4 h-4" />
            <span>{formatNumber(discussion.downvotes)}</span>
          </div>

          <div className="flex items-center space-x-1">
            <MessageSquare className="w-4 h-4" />
            <span>{formatNumber(discussion.replies.length)}</span>
          </div>

          <div className="flex items-center space-x-1">
            <Eye className="w-4 h-4" />
            <span>{formatNumber(discussion.views)}</span>
          </div>
        </div>

        <Link
          to={`/discussion/${discussion.id}`}
          className="btn-secondary text-sm px-4 py-2"
        >
          {discussion.isQuestion ? 'View Question' : 'View Discussion'}
        </Link>
      </div>

      {/* Author Badges */}
      {discussion.author.badges.length > 0 && (
        <div className="flex items-center space-x-2 mt-3 pt-3 border-t border-gray-100">
          <span className="text-xs text-gray-500">Badges:</span>
          {discussion.author.badges.slice(0, 3).map((badge) => (
            <span
              key={badge.id}
              className="text-xs px-2 py-1 rounded-full bg-gray-100 text-gray-700 flex items-center space-x-1"
            >
              <span>{badge.icon}</span>
              <span>{badge.name}</span>
            </span>
          ))}
          {discussion.author.badges.length > 3 && (
            <span className="text-xs text-gray-500">
              +{discussion.author.badges.length - 3} more
            </span>
          )}
        </div>
      )}
    </div>
  );
};

export default DiscussionCard;
