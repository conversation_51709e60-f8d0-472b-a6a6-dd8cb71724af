export interface CryptoProject {
  id: string;
  name: string;
  symbol: string;
  logo: string;
  description: string;
  category: string;
  marketCap: number;
  price: number;
  priceChange24h: number;
  volume24h: number;
  circulatingSupply: number;
  totalSupply: number;
  rank: number;
  website: string;
  whitepaper: string;
  socialLinks: {
    twitter?: string;
    telegram?: string;
    discord?: string;
    github?: string;
  };
  tags: string[];
  launchDate: string;
  verified: boolean;
  communityScore: number;
  developerScore: number;
  liquidityScore: number;
  publicInterestScore: number;
}

export interface Discussion {
  id: string;
  title: string;
  content: string;
  author: User;
  projectId?: string;
  tags: string[];
  createdAt: string;
  updatedAt: string;
  upvotes: number;
  downvotes: number;
  replies: Comment[];
  isQuestion: boolean;
  isAnswered?: boolean;
  bestAnswer?: Comment;
  views: number;
}

export interface Comment {
  id: string;
  content: string;
  author: User;
  createdAt: string;
  updatedAt: string;
  upvotes: number;
  downvotes: number;
  replies: Comment[];
  isVerified: boolean;
  isBestAnswer?: boolean;
}

export interface User {
  id: string;
  username: string;
  avatar: string;
  reputation: number;
  joinDate: string;
  isVerified: boolean;
  badges: Badge[];
}

export interface Badge {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
}

export interface MarketData {
  totalMarketCap: number;
  totalVolume24h: number;
  btcDominance: number;
  ethDominance: number;
  marketChange24h: number;
}

export interface TrendingProject {
  id: string;
  name: string;
  symbol: string;
  logo: string;
  priceChange24h: number;
  volume24h: number;
  socialScore: number;
}

// Ethereum provider types
declare global {
  interface Window {
    ethereum?: {
      request: (args: { method: string; params?: any[] }) => Promise<any>;
      on: (event: string, callback: (...args: any[]) => void) => void;
      removeListener: (event: string, callback: (...args: any[]) => void) => void;
      isMetaMask?: boolean;
    };
  }
}
