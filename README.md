# Project Crypto Land Frontend

Một ứng dụng web hiện đại kết hợp giữa nền tảng thông tin dự án crypto và diễn đàn cộng đồng, đ<PERSON><PERSON><PERSON> thiết kế với giao diện tương tự CoinGecko.

## 🚀 Tính năng chính

### 📊 Thông tin dự án Crypto
- **Market Overview**: Tổng quan thị trường với tổng vốn hóa, kh<PERSON><PERSON> lư<PERSON> giao dịch, BTC dominance
- **Project Rankings**: Xếp hạng các dự án theo market cap, volume, và các chỉ số khác
- **Detailed Analytics**: Thông tin chi tiết về giá, supply, social links, whitepaper
- **Community Scores**: Điểm đánh giá từ cộng đồng, developer, liquidity, và public interest
- **Search & Filter**: Tì<PERSON> kiếm và lọc dự án theo category, tags, và các tiêu chí khác

### 💬 Diễn đàn cộng đồng
- **Q&A System**: <PERSON><PERSON> thống hỏi đáp với voting, best answer marking
- **Discussion Threads**: Thảo luận về các chủ đề crypto
- **User Reputation**: Hệ thống uy tín và badges cho người dùng
- **Tag System**: Phân loại thảo luận theo tags và categories
- **Moderation Tools**: Công cụ báo cáo và quản lý nội dung

### 🎨 Giao diện người dùng
- **Modern Design**: Thiết kế hiện đại với Tailwind CSS
- **Responsive Layout**: Tương thích với mọi thiết bị
- **Dark/Light Theme**: Hỗ trợ chế độ sáng/tối
- **Interactive Elements**: Các thành phần tương tác mượt mà
- **Accessibility**: Tuân thủ tiêu chuẩn accessibility

## 🛠️ Công nghệ sử dụng

- **Frontend Framework**: React 18 + TypeScript
- **Styling**: Tailwind CSS + Custom CSS
- **Routing**: React Router DOM
- **Icons**: Lucide React
- **Build Tool**: Create React App
- **Package Manager**: npm

## 📁 Cấu trúc dự án

```
src/
├── components/          # React components
│   ├── Layout.tsx      # Layout chính với navigation
│   ├── ProjectCard.tsx # Card hiển thị dự án
│   └── DiscussionCard.tsx # Card hiển thị thảo luận
├── pages/              # Các trang chính
│   ├── HomePage.tsx    # Trang chủ
│   ├── ProjectsPage.tsx # Trang danh sách dự án
│   ├── ProjectDetailPage.tsx # Trang chi tiết dự án
│   ├── ForumPage.tsx   # Trang diễn đàn
│   └── DiscussionPage.tsx # Trang chi tiết thảo luận
├── types/              # TypeScript type definitions
│   └── index.ts        # Các interface chính
├── data/               # Mock data
│   └── mockData.ts     # Dữ liệu mẫu cho development
├── App.tsx             # Component chính
└── index.tsx           # Entry point
```

## 🚀 Cài đặt và chạy

### Yêu cầu hệ thống
- Node.js 16+
- npm 8+

### Cài đặt dependencies
```bash
npm install
```

### Chạy ứng dụng development
```bash
npm start
```

Ứng dụng sẽ chạy tại `http://localhost:3000`

### Build production
```bash
npm run build
```

### Chạy tests
```bash
npm test
```

## 📱 Các trang chính

### 🏠 Trang chủ (HomePage)
- Market overview với tổng quan thị trường
- Top projects được xếp hạng
- Trending projects
- Recent discussions
- Call-to-action buttons

### 📈 Trang dự án (ProjectsPage)
- Danh sách tất cả dự án crypto
- Bộ lọc theo category, tags
- Sắp xếp theo nhiều tiêu chí
- Search functionality
- Statistics và quick actions

### 🔍 Trang chi tiết dự án (ProjectDetailPage)
- Thông tin chi tiết về dự án
- Price charts và market data
- Supply information
- Community scores
- Social links và whitepaper
- Related discussions
- Tabs: Overview, Social & Links, Discussions, Analysis

### 💬 Trang diễn đàn (ForumPage)
- Danh sách thảo luận và câu hỏi
- Filter theo type (questions/discussions)
- Filter theo tags
- Sorting options
- Community guidelines
- Popular tags

### 💭 Trang thảo luận (DiscussionPage)
- Nội dung thảo luận chi tiết
- Hệ thống replies và comments
- Voting system (upvote/downvote)
- User badges và reputation
- Related discussions

## 🎨 Design System

### Colors
- **Primary**: Blue gradient (#0ea5e9 to #8b5cf6)
- **Success**: Green (#00ff88)
- **Warning**: Yellow (#ffbb00)
- **Danger**: Red (#ff4444)
- **Info**: Blue (#3b82f6)

### Components
- **Cards**: White background với shadow và border radius
- **Buttons**: Primary (blue) và Secondary (gray) variants
- **Badges**: Colored tags cho categories và status
- **Inputs**: Styled form fields với focus states
- **Navigation**: Sidebar với active states

### Typography
- **Font Family**: Inter (sans-serif)
- **Monospace**: JetBrains Mono
- **Headings**: Bold weights cho hierarchy
- **Body**: Regular weight cho readability

## 🔮 Tính năng tương lai

- [ ] **Real-time Data**: Tích hợp WebSocket cho price updates
- [ ] **Charts**: TradingView charts và technical analysis
- [ ] **Portfolio Tracking**: Theo dõi portfolio cá nhân
- [ ] **Notifications**: Push notifications cho price alerts
- [ ] **Mobile App**: React Native app
- [ ] **Social Features**: Following users, private messages
- [ ] **Content Moderation**: AI-powered content filtering
- [ ] **Analytics Dashboard**: Advanced market insights

## 🤝 Đóng góp

1. Fork dự án
2. Tạo feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Mở Pull Request

## 📄 License

Dự án này được phát hành dưới MIT License - xem file [LICENSE](LICENSE) để biết thêm chi tiết.

## 📞 Liên hệ

- **Tác giả**: CryptoLand Team
- **Email**: <EMAIL>
- **Website**: https://cryptoland.com
- **Discord**: https://discord.gg/cryptoland

## 🙏 Acknowledgments

- **CoinGecko**: Inspiration cho design và features
- **Tailwind CSS**: Utility-first CSS framework
- **React Community**: Amazing ecosystem và tools
- **Crypto Community**: Feedback và suggestions

---

⭐ Nếu dự án này hữu ích, hãy cho chúng tôi một star!
